#include "feilian-specialmodule/oversetMesh/KDTManager.h"

namespace Floga
{
    // ==================== KDTSearcherPool实现 ====================

    KDT *KDTManager::KDTSearcherPool::acquire()
    {
        stats.totalAcquires++;

        if (!available.empty())
        {
            KDT *searcher = available.front();
            available.pop();
            stats.poolHits++;
            stats.currentPoolSize = available.size();
            return searcher;
        }

        // 池中没有可用对象，创建新的
        auto newSearcher = std::make_unique<KDT>(dimension);
        KDT *result = newSearcher.get();
        pool.push_back(std::move(newSearcher));

        stats.poolMisses++;
        stats.currentPoolSize = available.size();
        if (pool.size() > stats.maxPoolSize)
        {
            stats.maxPoolSize = pool.size();
        }

        return result;
    }

    void KDTManager::KDTSearcherPool::release(KDT *searcher)
    {
        if (searcher == nullptr)
            return;

        stats.totalReleases++;

        // 如果池未满，将对象放回池中
        if (available.size() < maxPoolSize)
        {
            available.push(searcher);
            stats.currentPoolSize = available.size();
        }
        // 否则对象会在pool析构时自动释放
    }

    void KDTManager::KDTSearcherPool::clear()
    {
        while (!available.empty())
        {
            available.pop();
        }
        pool.clear();
        stats = PoolingStats(); // 重置统计信息
    }

    // ==================== OversetKDTManager主类实现 ====================

    KDTManager::KDTManager()
    {
        lastUpdateTime = std::chrono::high_resolution_clock::now();
    }

    KDTManager::KDTManager(int dimension_, ZoneManager *zoneManager_)
        : dimension(dimension_), zoneManager(zoneManager_)
    {
        initialize(dimension_, zoneManager_);
    }

    KDTManager::~KDTManager()
    {
        clear();
    }

    void KDTManager::initialize(int dimension_, ZoneManager *zoneManager_)
    {
        if (initialized)
        {
            Print("Warning: OversetKDTManager已经初始化，重新初始化将清理现有数据");
            clear();
        }

        dimension = dimension_;
        zoneManager = zoneManager_;

        if (dimension <= 0 || dimension > 3)
        {
            Print("Error: OversetKDTManager::initialize: 无效的网格维度 %d", dimension);
            return;
        }

        if (zoneManager == nullptr)
        {
            Print("Warning: OversetKDTManager::initialize: ZoneManager指针为空");
        }

        // 创建KDT搜索器池
        kdtPool = std::make_unique<KDTSearcherPool>(dimension);

        initialized = true;
        lastUpdateTime = std::chrono::high_resolution_clock::now();

        Print("OversetKDTManager初始化完成，维度=%d", dimension);
    }

    void KDTManager::initializeKDTSearchers(Mesh *localMesh, int nZones)
    {
        if (!initialized)
        {
            Print("Error: OversetKDTManager::initializeKDTSearchers: 管理器未初始化");
            return;
        }

        if (localMesh == nullptr)
        {
            Print("Error: OversetKDTManager::initializeKDTSearchers: 网格指针为空");
            return;
        }

        if (nZones <= 0)
        {
            Print("Error: OversetKDTManager::initializeKDTSearchers: 无效的子域数量 %d", nZones);
            return;
        }

        // 清理现有搜索器
        clear();

        // 初始化搜索器容器
        kdtSearchers.resize(nZones, nullptr);
        zoneNeedsRebuild.resize(nZones, true);

        // 为每个子域创建KDT搜索器
        for (int zoneID = 0; zoneID < nZones; ++zoneID)
        {
            try
            {
                // 创建子域网格
                Mesh *zoneMesh = createZoneMesh(zoneID, localMesh);
                if (zoneMesh != nullptr)
                {
                    // 创建KDT搜索器
                    kdtSearchers[zoneID] = createKDTSearcher(zoneID, zoneMesh);
                    zoneNeedsRebuild[zoneID] = false;
                }
                else
                {
                    Print("Warning: 子域 %d 的网格创建失败", zoneID);
                }
            }
            catch (const std::exception &e)
            {
                Print("Error: 创建子域 %d 的KDT搜索器时发生异常: %s", zoneID, e.what());
                kdtSearchers[zoneID] = nullptr;
            }
        }

        // 强制更新全局树信息
        treeInfoNeedsUpdate = true;
        updateGlobalTreeInfo();

        Print("KDT搜索器初始化完成，成功创建 %d 个子域搜索器",
              static_cast<int>(std::count_if(kdtSearchers.begin(), kdtSearchers.end(),
                                             [](KDT *ptr)
                                             { return ptr != nullptr; })));
    }

    KDT *KDTManager::getKDTSearcher(int zoneID) const
    {
        if (!initialized)
        {
            Print("Warning: OversetKDTManager::getKDTSearcher: 管理器未初始化");
            return nullptr;
        }

        if (zoneID < 0 || zoneID >= static_cast<int>(kdtSearchers.size()))
        {
            Print("Warning: OversetKDTManager::getKDTSearcher: 无效的子域编号 %d", zoneID);
            return nullptr;
        }

        return kdtSearchers[zoneID];
    }

    void KDTManager::clear()
    {
        // 清理KDT搜索器
        for (auto *searcher : kdtSearchers)
        {
            if (poolingEnabled && kdtPool && searcher)
            {
                kdtPool->release(searcher);
            }
            else
            {
                delete searcher;
            }
        }
        kdtSearchers.clear();

        // 清理池
        if (kdtPool)
        {
            kdtPool->clear();
        }

        // 清理其他数据
        cachedGlobalTreeInfo.clear();
        zoneNeedsRebuild.clear();
        treeInfoNeedsUpdate = true;

        Print("OversetKDTManager已清理所有数据");
    }

    bool KDTManager::rebuildKDTTree(int zoneID, Mesh *localMesh)
    {
        if (!initialized || localMesh == nullptr)
        {
            return false;
        }

        if (zoneID < 0 || zoneID >= static_cast<int>(kdtSearchers.size()))
        {
            Print("Error: OversetKDTManager::rebuildKDTTree: 无效的子域编号 %d", zoneID);
            return false;
        }

        try
        {
            // 释放旧的搜索器
            if (kdtSearchers[zoneID])
            {
                if (poolingEnabled && kdtPool)
                {
                    kdtPool->release(kdtSearchers[zoneID]);
                }
                else
                {
                    delete kdtSearchers[zoneID];
                }
                kdtSearchers[zoneID] = nullptr;
            }

            // 创建新的搜索器
            Mesh *zoneMesh = createZoneMesh(zoneID, localMesh);
            if (zoneMesh != nullptr)
            {
                kdtSearchers[zoneID] = createKDTSearcher(zoneID, zoneMesh);
                zoneNeedsRebuild[zoneID] = false;
                treeInfoNeedsUpdate = true;

                Print("子域 %d 的KDT树重建成功", zoneID);
                return true;
            }
        }
        catch (const std::exception &e)
        {
            Print("Error: 重建子域 %d 的KDT树时发生异常: %s", zoneID, e.what());
        }

        return false;
    }

    bool KDTManager::incrementalUpdateKDTTree(int zoneID, Mesh *localMesh,
                                              const std::vector<Vector> &displacementField)
    {
        // 对于增量更新，当前实现采用重建策略
        // 未来可以实现真正的增量更新算法
        if (!shouldRebuildZone(zoneID, localMesh))
        {
            return true; // 不需要更新
        }

        Print("子域 %d 需要增量更新，采用重建策略", zoneID);
        return rebuildKDTTree(zoneID, localMesh);
    }

    const std::vector<TreeInfo> &KDTManager::getGlobalTreeInfo() const
    {
        if (treeInfoNeedsUpdate)
        {
            const_cast<KDTManager *>(this)->updateGlobalTreeInfo();
        }
        return cachedGlobalTreeInfo;
    }

    void KDTManager::updateGlobalTreeInfo()
    {
        if (!initialized)
        {
            return;
        }

        cachedGlobalTreeInfo.clear();

        // 收集所有有效搜索器的树信息
        for (size_t i = 0; i < kdtSearchers.size(); ++i)
        {
            if (kdtSearchers[i] != nullptr)
            {
                try
                {
                    const std::vector<TreeInfo> &localTreeInfo = kdtSearchers[i]->GetGlobalTreeInfo();
                    cachedGlobalTreeInfo.insert(cachedGlobalTreeInfo.end(),
                                                localTreeInfo.begin(), localTreeInfo.end());
                }
                catch (const std::exception &e)
                {
                    Print("Warning: 获取子域 %d 的树信息时发生异常: %s",
                          static_cast<int>(i), e.what());
                }
            }
        }

        treeInfoNeedsUpdate = false;
        lastUpdateTime = std::chrono::high_resolution_clock::now();

        Print("全局树信息更新完成，包含 %d 个树信息",
              static_cast<int>(cachedGlobalTreeInfo.size()));
    }

    void KDTManager::setPoolingEnabled(bool enable, size_t poolSize)
    {
        poolingEnabled = enable;

        if (enable)
        {
            if (!kdtPool)
            {
                kdtPool = std::make_unique<KDTSearcherPool>(dimension, poolSize);
            }
            else
            {
                kdtPool->setMaxPoolSize(poolSize);
            }
            Print("KDT搜索器池化已启用，池大小=%d", static_cast<int>(poolSize));
        }
        else
        {
            Print("KDT搜索器池化已禁用");
        }

        // 更新统计信息
        if (kdtPool)
        {
            poolingStats = kdtPool->getStats();
        }
    }

    // ==================== 私有方法实现 ====================

    Mesh *KDTManager::createZoneMesh(int zoneID, Mesh *localMesh)
    {
        if (!zoneManager || !localMesh)
        {
            return nullptr;
        }

        try
        {
            // 这里需要根据实际的ZoneManager接口来实现
            // 假设ZoneManager提供了获取子域网格的方法
            // 实际实现需要根据feilian的ZoneManager接口调整

            // 临时实现：直接返回本地网格
            // 在实际使用中，应该根据zoneID提取对应的子域网格
            Print("Warning: createZoneMesh使用临时实现，返回完整本地网格");
            return localMesh;
        }
        catch (const std::exception &e)
        {
            Print("Error: 创建子域 %d 网格时发生异常: %s", zoneID, e.what());
            return nullptr;
        }
    }

    KDT *KDTManager::createKDTSearcher(int zoneID, Mesh *zoneMesh)
    {
        if (!zoneMesh)
        {
            return nullptr;
        }

        try
        {
            KDT *searcher = nullptr;

            if (poolingEnabled && kdtPool)
            {
                searcher = kdtPool->acquire();
                poolingStats = kdtPool->getStats();
            }
            else
            {
                searcher = new KDT(dimension);
            }

            if (searcher)
            {
                // 为子域网格创建KDT树
                searcher->CreateElementKDTree(zoneMesh);
                Print("子域 %d 的KDT搜索器创建成功", zoneID);
            }

            return searcher;
        }
        catch (const std::exception &e)
        {
            Print("Error: 创建子域 %d 的KDT搜索器时发生异常: %s", zoneID, e.what());
            return nullptr;
        }
    }

    bool KDTManager::shouldRebuildZone(int zoneID, Mesh *localMesh) const
    {
        if (zoneID < 0 || zoneID >= static_cast<int>(zoneNeedsRebuild.size()))
        {
            return true;
        }

        // 检查是否标记为需要重建
        if (zoneNeedsRebuild[zoneID])
        {
            return true;
        }

        // 检查时间间隔（可选的重建策略）
        auto currentTime = std::chrono::high_resolution_clock::now();
        auto timeDiff = std::chrono::duration_cast<std::chrono::seconds>(
                            currentTime - lastUpdateTime)
                            .count();

        // 如果超过一定时间间隔，考虑重建（这里设置为60秒）
        if (timeDiff > 60)
        {
            Print("子域 %d 超过时间阈值，建议重建KDT树", zoneID);
            return true;
        }

        return false;
    }

} // namespace Floga
