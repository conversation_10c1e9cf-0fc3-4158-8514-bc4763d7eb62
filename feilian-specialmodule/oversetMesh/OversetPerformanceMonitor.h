////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetPerformanceMonitor.h
//! <AUTHOR>
//! @brief 重叠网格性能监控器
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetPerformanceMonitor_
#define _specialModule_oversetMesh_OversetPerformanceMonitor_

#include <unordered_map>
#include <chrono>

namespace Floga
{
    /**
     * @brief 重叠网格性能监控器
     *
     * 负责收集和统计搜索性能指标，提供性能分析功能
     */
    class OversetPerformanceMonitor
    {
    public:
        /**
         * @brief 性能指标结构
         */
        struct SearchMetrics
        {
            size_t totalSearches = 0;
            size_t successfulSearches = 0;
            size_t failedSearches = 0;
            double totalSearchTime = 0.0;
            double averageSearchTime = 0.0;
            double maxSearchTime = 0.0;
            std::unordered_map<int, size_t> zoneSearchCounts;
            std::unordered_map<int, size_t> zoneSuccessCounts;

            double getSuccessRate() const
            {
                return totalSearches > 0 ? (double)successfulSearches / totalSearches : 0.0;
            }

            void reset()
            {
                *this = SearchMetrics{};
            }
        };

        /**
         * @brief 并行通信指标结构
         */
        struct CommunicationMetrics
        {
            size_t totalMessages = 0;
            size_t totalDataTransferred = 0;
            double totalCommTime = 0.0;
            double averageCommTime = 0.0;
            double maxCommTime = 0.0;

            void reset()
            {
                *this = CommunicationMetrics{};
            }
        };

    public:
        /**
         * @brief 构造函数
         */
        OversetPerformanceMonitor() = default;

        /**
         * @brief 析构函数
         */
        ~OversetPerformanceMonitor() = default;

        /**
         * @brief 记录搜索开始
         * @return 搜索开始时间点
         */
        std::chrono::high_resolution_clock::time_point recordSearchStart();

        /**
         * @brief 记录搜索结束
         * @param startTime 搜索开始时间点
         * @param successful 是否搜索成功
         * @param zoneID 搜索的子域ID
         */
        void recordSearchEnd(const std::chrono::high_resolution_clock::time_point &startTime,
                             bool successful, int zoneID);

        /**
         * @brief 记录通信开始
         * @return 通信开始时间点
         */
        std::chrono::high_resolution_clock::time_point recordCommStart();

        /**
         * @brief 记录通信结束
         * @param startTime 通信开始时间点
         * @param dataSize 传输的数据大小
         */
        void recordCommEnd(const std::chrono::high_resolution_clock::time_point &startTime,
                           size_t dataSize);

        /**
         * @brief 获取搜索性能指标
         */
        const SearchMetrics &getSearchMetrics() const { return searchMetrics; }

        /**
         * @brief 获取通信性能指标
         */
        const CommunicationMetrics &getCommunicationMetrics() const { return commMetrics; }

        /**
         * @brief 重置所有性能指标
         */
        void resetAllMetrics();

        /**
         * @brief 打印性能报告
         */
        void printPerformanceReport() const;

        /**
         * @brief 导出性能数据到文件
         * @param filename 文件名
         */
        void exportMetricsToFile(const std::string &filename) const;

        /**
         * @brief 导出性能数据到CSV文件
         * @param filename 文件名
         */
        void exportMetricsToCSV(const std::string &filename) const;

        /**
         * @brief 导出性能数据到JSON文件
         * @param filename 文件名
         */
        void exportMetricsToJSON(const std::string &filename) const;

        /**
         * @brief 记录自定义事件
         * @param category 事件类别
         * @param name 事件名称
         * @param value 事件值
         */
        void recordEvent(const std::string &category, const std::string &name, double value);

        /**
         * @brief 记录内存使用情况
         * @param memoryUsage 内存使用量（字节）
         */
        void recordMemoryUsage(size_t memoryUsage);

        /**
         * @brief 获取内存使用统计
         */
        struct MemoryMetrics
        {
            size_t currentMemoryUsage = 0;
            size_t peakMemoryUsage = 0;
            size_t totalAllocations = 0;
            double averageMemoryUsage = 0.0;
            std::vector<std::pair<std::chrono::high_resolution_clock::time_point, size_t>> memoryHistory;

            void reset()
            {
                *this = MemoryMetrics{};
            }
        };

        const MemoryMetrics &getMemoryMetrics() const { return memoryMetrics; }

        /**
         * @brief 收集并汇总所有进程的性能数据
         */
        void gatherGlobalMetrics();

        /**
         * @brief 分析性能瓶颈
         */
        struct PerformanceBottleneck
        {
            std::string category;
            std::string description;
            double severity; // 0.0-1.0, 1.0表示最严重
            std::string recommendation;
        };

        std::vector<PerformanceBottleneck> analyzeBottlenecks() const;

        /**
         * @brief 启用/禁用详细监控模式
         * @param enable 是否启用
         */
        void setDetailedMonitoring(bool enable) { detailedMonitoring = enable; }

        /**
         * @brief 设置监控采样率
         * @param rate 采样率 (0.0-1.0)
         */
        void setSamplingRate(double rate) { samplingRate = std::max(0.0, std::min(1.0, rate)); }

    private:
        SearchMetrics searchMetrics;      // 搜索性能指标
        CommunicationMetrics commMetrics; // 通信性能指标
        MemoryMetrics memoryMetrics;      // 内存使用指标

        // 自定义事件记录
        std::unordered_map<std::string, std::unordered_map<std::string, std::vector<double>>> customEvents;

        // 控制参数
        bool detailedMonitoring = false; // 是否启用详细监控
        double samplingRate = 1.0;       // 监控采样率

        // 辅助方法
        bool shouldSample() const;
        std::string getCurrentTimeString() const;
        void writeCSVHeader(std::ofstream &file) const;
        void writeCSVData(std::ofstream &file) const;
        std::string generateJSONReport() const;
    };
} // namespace Floga

#endif
