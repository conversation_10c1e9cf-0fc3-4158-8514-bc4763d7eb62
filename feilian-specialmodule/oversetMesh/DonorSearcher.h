////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetDonorSearcher.h
//! <AUTHOR>
//! @brief 重叠网格贡献单元搜索器（主类）
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_DonorSearcher_
#define _specialModule_oversetMesh_DonorSearcher_

#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "feilian-specialmodule/oversetMesh/Acceptor.h"
#include "feilian-specialmodule/oversetMesh/OversetKDTManager.h"
#include "feilian-specialmodule/oversetMesh/OversetPerformanceMonitor.h"
#include "feilian-specialmodule/oversetMesh/ParallelCoordinator.h"
#include "feilian-specialmodule/oversetMesh/OversetDynamicMeshManager.h"
#include "feilian-specialmodule/oversetMesh/OversetToleranceManager.h"

namespace Floga
{
    /**
     * @brief 重叠网格贡献单元搜索器
     *
     * 专注于核心搜索功能，通过组合模式集成各个功能模块
     */
    class DonorSearcher
    {
    public:
        /**
         * @brief 构造函数
         * @param mesh_ 网格指针
         * @param zoneManager_ 域管理器指针
         * @param elemTypeField_ 单元类型场指针
         */
        DonorSearcher(Mesh *mesh_,
                      ZoneManager *zoneManager_,
                      ElementField<int> *elemTypeField_);

        /**
         * @brief 析构函数
         */
        ~DonorSearcher();

        /**
         * @brief 初始化贡献单元搜索器
         */
        void Initialize();

        /**
         * @brief 为批量插值单元并行搜索贡献单元
         * @param groupedAcpts 按进程分组的插值单元列表
         * @param searchResults 搜索结果
         */
        void ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts, Set<Acceptor> &searchResults);

        /**
         * @brief 为单个插值单元列表搜索贡献单元
         * @param acptList 插值单元列表
         */
        void ChunkDonorSearch(List<Acceptor> &acptList);

        /**
         * @brief 为单个点搜索贡献单元
         * @param srcNode 插值点坐标
         * @param zoneID 目标子域编号
         * @return int 贡献单元编号，未找到时返回-1
         */
        int DonorSearchWithKDT(const Node &srcNode, int zoneID);

        /**
         * @brief 将插值单元按照可能存在贡献单元的进程分组
         * @param searchElemID 需要搜索贡献单元的单元编号集合
         * @param groupedAcceptors 分组结果
         */
        void GroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcceptors);

        /**
         * @brief 将插值单元按照贡献单元的进程号分组
         * @param srcAcpts 源插值单元集合
         * @param groupedAcpts 分组结果
         */
        void GroupingAcceptors(const Set<Acceptor> &srcAcpts, List<List<Acceptor>> &groupedAcpts);

        /**
         * @brief 判断点是否在网格单元内部
         * @param node 坐标点
         * @param elem 网格单元
         * @return true 在内部，false 在外部
         */
        bool NodeInElem(const Node &node, const Element &elem);

        /**
         * @brief 获取全局树信息
         * @return 全局树信息向量
         */
        const std::vector<TreeInfo> &GetGlobalTreeInfo() const;

        /**
         * @brief 获取贡献单元标记数组
         * @return 贡献单元标记数组
         */
        const List<int> &GetAcceptorDonorFlag() const { return acceptorDonorFlag; }

        /**
         * @brief 清理搜索数据
         */
        void Clear();

        // ==================== 功能模块访问接口 ====================

        /**
         * @brief 获取性能监控器
         */
        OversetPerformanceMonitor *getPerformanceMonitor() { return performanceMonitor.get(); }

        /**
         * @brief 获取并行协调器
         */
        ParallelCoordinator *getParallelCoordinator() { return parallelCoordinator.get(); }

        /**
         * @brief 获取动态网格管理器
         */
        OversetDynamicMeshManager *getDynamicMeshManager() { return dynamicMeshManager.get(); }

        /**
         * @brief 获取容差管理器
         */
        OversetToleranceManager *getToleranceManager() { return toleranceManager.get(); }

        // ==================== 便捷接口（保持向后兼容） ====================

        /**
         * @brief 启用动态网格模式
         */
        void enableDynamicMesh(Scalar rebuildThreshold = 0.1, double cacheValidityTime = 1.0);

        /**
         * @brief 更新网格运动信息
         */
        void updateMeshMotion(const std::vector<Vector> &displacementField,
                              double timeStep, int stepNumber);

        /**
         * @brief 获取搜索性能指标
         */
        const OversetPerformanceMonitor::SearchMetrics &getSearchMetrics() const;

        /**
         * @brief 获取动态网格统计指标
         */
        const OversetDynamicMeshManager::DynamicMeshMetrics &getDynamicMeshMetrics() const;

        /**
         * @brief 设置分组策略
         */
        void setGroupingStrategy(ParallelCoordinator::GroupingStrategy strategy);

        /**
         * @brief 设置容差策略
         */
        void setToleranceStrategy(OversetToleranceManager::ToleranceStrategy strategy);

        // ==================== 搜索结果缓存管理接口 ====================

        /**
         * @brief 查询缓存中的搜索结果
         * @param elemID 单元编号
         * @return 指向Acceptor对象的指针，未找到时返回nullptr
         */
        const Acceptor *GetSearchResult(int elemID) const;

        /**
         * @brief 更新搜索结果缓存
         * @param acceptor 搜索得到的Acceptor对象
         */
        void InsertSearchResult(const Acceptor &acceptor);

        /**
         * @brief 批量更新搜索结果缓存
         * @param acceptors 搜索结果列表
         */
        void InsertSearchResults(const Set<Acceptor> &acceptors);

        /**
         * @brief 获取缓存大小
         * @return 缓存中的Acceptor数量
         */
        size_t GetSearchResultsSize() const;

        /**
         * @brief 清理搜索结果缓存
         */
        void ClearSearchResults();

        /**
         * @brief 获取缓存统计信息
         */
        struct SearchResultsCacheStats
        {
            size_t totalCached;    // 总缓存数量
            size_t validResults;   // 有效结果数量（找到贡献单元）
            size_t invalidResults; // 无效结果数量（未找到贡献单元）
            size_t memoryUsage;    // 内存使用量（字节）
        };

        SearchResultsCacheStats GetSearchResultsCacheStats() const;

    private:
        /**
         * @brief 执行实际的贡献单元搜索
         */
        void performActualSearch(Acceptor &acpt);

        /**
         * @brief 合并各进程搜索结果，选择最优贡献单元
         */
        void SelectBestDonor(const List<List<Acceptor>> &groupedSearchResults, List<Acceptor> &mergedResults);

        /**
         * @brief 获取缓存中的搜索结果（内部方法）
         */
        const Acceptor *getSearchResult(int elemID) const;

        /**
         * @brief 更新搜索结果缓存（内部方法）
         */
        void updateSearchResult(int elemID, const Acceptor &acceptor);

    private:
        // 基础数据
        Mesh *localMesh;                  // 当前进程网格指针
        ZoneManager *zoneManager;         // 域管理器指针
        ElementField<int> *elemTypeField; // 网格单元重叠类型场
        MPICommunicator mpi_world;        // MPI通信器

        int n_Zones;       // 网格子域个数
        Mesh::MeshDim dim; // 网格维度
        int processorID;   // 当前进程ID
        int nProcessor;    // 总进程数

        // 搜索类
        UniquePtr<KDT> kdt; // KDT搜索器

        // 搜索结果
        Map<int, Acceptor> searchResults; // 搜索结果缓存容器,插入元素时进行合并
        List<int> acceptorDonorFlag;      // 用于标记插值单元是否被用作了贡献单元

        // 缓存管理
        bool enableCache;              // 是否启用缓存
        size_t maxCacheSize;           // 最大缓存大小
        mutable size_t cacheHitCount;  // 缓存命中次数
        mutable size_t cacheMissCount; // 缓存未命中次数

        // 功能模块（组合模式）
        std::unique_ptr<OversetPerformanceMonitor> performanceMonitor;
        std::unique_ptr<ParallelCoordinator> parallelCoordinator;
        std::unique_ptr<OversetDynamicMeshManager> dynamicMeshManager;
        std::unique_ptr<OversetToleranceManager> toleranceManager;
        std::unique_ptr<OversetKDTManager> kdtManager;
    };
} // namespace Floga

#endif
