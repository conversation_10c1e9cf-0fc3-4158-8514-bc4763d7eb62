////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetPerformanceMonitor.cpp
//! <AUTHOR>
//! @brief 重叠网格性能监控器实现
//! @date 2024-03-12
//------------------------------------------------------------------------------

#include "OversetPerformanceMonitor.h"
#include "basic/common/SystemControl.h"
#include "basic/common/MPI.h"
#include <fstream>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <random>
#include <ctime>

namespace Floga
{
    std::chrono::high_resolution_clock::time_point OversetPerformanceMonitor::recordSearchStart()
    {
        return std::chrono::high_resolution_clock::now();
    }

    void OversetPerformanceMonitor::recordSearchEnd(const std::chrono::high_resolution_clock::time_point &startTime,
                                                    bool successful, int zoneID)
    {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        double searchTime = duration.count() / 1000.0; // 转换为毫秒

        // 更新基本统计
        searchMetrics.totalSearches++;
        searchMetrics.totalSearchTime += searchTime;
        searchMetrics.maxSearchTime = std::max(searchMetrics.maxSearchTime, searchTime);

        if (successful)
        {
            searchMetrics.successfulSearches++;
            searchMetrics.zoneSuccessCounts[zoneID]++;
        }
        else
        {
            searchMetrics.failedSearches++;
        }

        // 更新分区域统计
        searchMetrics.zoneSearchCounts[zoneID]++;

        // 更新平均搜索时间
        if (searchMetrics.totalSearches > 0)
        {
            searchMetrics.averageSearchTime = searchMetrics.totalSearchTime / searchMetrics.totalSearches;
        }
    }

    std::chrono::high_resolution_clock::time_point OversetPerformanceMonitor::recordCommStart()
    {
        return std::chrono::high_resolution_clock::now();
    }

    void OversetPerformanceMonitor::recordCommEnd(const std::chrono::high_resolution_clock::time_point &startTime,
                                                  size_t dataSize)
    {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        double commTime = duration.count() / 1000.0; // 转换为毫秒

        // 更新通信统计
        commMetrics.totalMessages++;
        commMetrics.totalDataTransferred += dataSize;
        commMetrics.totalCommTime += commTime;
        commMetrics.maxCommTime = std::max(commMetrics.maxCommTime, commTime);

        // 更新平均通信时间
        if (commMetrics.totalMessages > 0)
        {
            commMetrics.averageCommTime = commMetrics.totalCommTime / commMetrics.totalMessages;
        }
    }

    void OversetPerformanceMonitor::resetAllMetrics()
    {
        searchMetrics.reset();
        commMetrics.reset();
    }

    void OversetPerformanceMonitor::printPerformanceReport() const
    {
        if (GetMPIRank() == 0)
        {
            Print("=== 重叠网格性能监控报告 ===");
            Print("");

            // 搜索性能报告
            Print(">> 供体搜索性能统计:");
            Print("   总搜索次数: " + std::to_string(searchMetrics.totalSearches));
            Print("   成功搜索次数: " + std::to_string(searchMetrics.successfulSearches));
            Print("   失败搜索次数: " + std::to_string(searchMetrics.failedSearches));

            std::ostringstream oss;
            oss << std::fixed << std::setprecision(2) << searchMetrics.getSuccessRate() * 100.0;
            Print("   搜索成功率: " + oss.str() + "%");

            oss.str("");
            oss << std::fixed << std::setprecision(3) << searchMetrics.totalSearchTime;
            Print("   总搜索时间: " + oss.str() + " ms");

            oss.str("");
            oss << std::fixed << std::setprecision(3) << searchMetrics.averageSearchTime;
            Print("   平均搜索时间: " + oss.str() + " ms");

            oss.str("");
            oss << std::fixed << std::setprecision(3) << searchMetrics.maxSearchTime;
            Print("   最大搜索时间: " + oss.str() + " ms");

            // 分区域统计
            if (!searchMetrics.zoneSearchCounts.empty())
            {
                Print("   分区域搜索统计:");
                for (const auto &pair : searchMetrics.zoneSearchCounts)
                {
                    int zoneID = pair.first;
                    size_t searchCount = pair.second;
                    size_t successCount = 0;
                    auto it = searchMetrics.zoneSuccessCounts.find(zoneID);
                    if (it != searchMetrics.zoneSuccessCounts.end())
                    {
                        successCount = it->second;
                    }

                    double successRate = searchCount > 0 ? (double)successCount / searchCount * 100.0 : 0.0;
                    oss.str("");
                    oss << "     区域 " << zoneID << ": " << searchCount << " 次搜索, "
                        << std::fixed << std::setprecision(1) << successRate << "% 成功率";
                    Print(oss.str());
                }
            }

            Print("");

            // 通信性能报告
            Print(">> MPI通信性能统计:");
            Print("   总消息数量: " + std::to_string(commMetrics.totalMessages));
            Print("   总传输数据量: " + std::to_string(commMetrics.totalDataTransferred) + " bytes");

            oss.str("");
            oss << std::fixed << std::setprecision(3) << commMetrics.totalCommTime;
            Print("   总通信时间: " + oss.str() + " ms");

            oss.str("");
            oss << std::fixed << std::setprecision(3) << commMetrics.averageCommTime;
            Print("   平均通信时间: " + oss.str() + " ms");

            oss.str("");
            oss << std::fixed << std::setprecision(3) << commMetrics.maxCommTime;
            Print("   最大通信时间: " + oss.str() + " ms");

            if (commMetrics.totalCommTime > 0.0)
            {
                double bandwidth = (double)commMetrics.totalDataTransferred / (commMetrics.totalCommTime / 1000.0) / (1024.0 * 1024.0);
                oss.str("");
                oss << std::fixed << std::setprecision(2) << bandwidth;
                Print("   平均带宽: " + oss.str() + " MB/s");
            }

            Print("=== 性能监控报告结束 ===");
            Print("");
        }
    }

    void OversetPerformanceMonitor::exportMetricsToFile(const std::string &filename) const
    {
        // 基本实现，后续会扩展
        if (GetMPIRank() == 0)
        {
            std::ofstream file(filename);
            if (file.is_open())
            {
                file << "# 重叠网格性能监控数据导出" << std::endl;
                file << "# 生成时间: " << getCurrentTimeString() << std::endl;
                file << std::endl;

                file << "[搜索性能统计]" << std::endl;
                file << "总搜索次数=" << searchMetrics.totalSearches << std::endl;
                file << "成功搜索次数=" << searchMetrics.successfulSearches << std::endl;
                file << "失败搜索次数=" << searchMetrics.failedSearches << std::endl;
                file << "搜索成功率=" << std::fixed << std::setprecision(4) << searchMetrics.getSuccessRate() << std::endl;
                file << "总搜索时间(ms)=" << std::fixed << std::setprecision(3) << searchMetrics.totalSearchTime << std::endl;
                file << "平均搜索时间(ms)=" << std::fixed << std::setprecision(3) << searchMetrics.averageSearchTime << std::endl;
                file << "最大搜索时间(ms)=" << std::fixed << std::setprecision(3) << searchMetrics.maxSearchTime << std::endl;

                file << std::endl;
                file << "[通信性能统计]" << std::endl;
                file << "总消息数量=" << commMetrics.totalMessages << std::endl;
                file << "总传输数据量(bytes)=" << commMetrics.totalDataTransferred << std::endl;
                file << "总通信时间(ms)=" << std::fixed << std::setprecision(3) << commMetrics.totalCommTime << std::endl;
                file << "平均通信时间(ms)=" << std::fixed << std::setprecision(3) << commMetrics.averageCommTime << std::endl;
                file << "最大通信时间(ms)=" << std::fixed << std::setprecision(3) << commMetrics.maxCommTime << std::endl;

                file << std::endl;
                file << "[内存使用统计]" << std::endl;
                file << "当前内存使用(bytes)=" << memoryMetrics.currentMemoryUsage << std::endl;
                file << "峰值内存使用(bytes)=" << memoryMetrics.peakMemoryUsage << std::endl;
                file << "总分配次数=" << memoryMetrics.totalAllocations << std::endl;
                file << "平均内存使用(bytes)=" << std::fixed << std::setprecision(0) << memoryMetrics.averageMemoryUsage << std::endl;

                file.close();
                Print("性能监控数据已导出到文件: " + filename);
            }
            else
            {
                WarningContinue("无法打开文件进行性能数据导出: " + filename);
            }
        }
    }

    void OversetPerformanceMonitor::exportMetricsToCSV(const std::string &filename) const
    {
        if (GetMPIRank() == 0)
        {
            std::ofstream file(filename);
            if (file.is_open())
            {
                writeCSVHeader(file);
                writeCSVData(file);
                file.close();
                Print("性能监控数据已导出到CSV文件: " + filename);
            }
            else
            {
                WarningContinue("无法打开CSV文件进行性能数据导出: " + filename);
            }
        }
    }

    void OversetPerformanceMonitor::exportMetricsToJSON(const std::string &filename) const
    {
        if (GetMPIRank() == 0)
        {
            std::ofstream file(filename);
            if (file.is_open())
            {
                file << generateJSONReport();
                file.close();
                Print("性能监控数据已导出到JSON文件: " + filename);
            }
            else
            {
                WarningContinue("无法打开JSON文件进行性能数据导出: " + filename);
            }
        }
    }

    void OversetPerformanceMonitor::recordEvent(const std::string &category, const std::string &name, double value)
    {
        if (shouldSample())
        {
            customEvents[category][name].push_back(value);
        }
    }

    void OversetPerformanceMonitor::recordMemoryUsage(size_t memoryUsage)
    {
        if (shouldSample())
        {
            memoryMetrics.currentMemoryUsage = memoryUsage;
            memoryMetrics.peakMemoryUsage = std::max(memoryMetrics.peakMemoryUsage, memoryUsage);
            memoryMetrics.totalAllocations++;

            // 更新平均内存使用
            if (memoryMetrics.totalAllocations > 0)
            {
                memoryMetrics.averageMemoryUsage =
                    (memoryMetrics.averageMemoryUsage * (memoryMetrics.totalAllocations - 1) + memoryUsage) /
                    memoryMetrics.totalAllocations;
            }

            // 记录内存使用历史（限制历史记录数量以避免内存泄漏）
            if (detailedMonitoring)
            {
                auto now = std::chrono::high_resolution_clock::now();
                memoryMetrics.memoryHistory.emplace_back(now, memoryUsage);

                // 限制历史记录数量
                const size_t maxHistorySize = 10000;
                if (memoryMetrics.memoryHistory.size() > maxHistorySize)
                {
                    memoryMetrics.memoryHistory.erase(memoryMetrics.memoryHistory.begin());
                }
            }
        }
    }

    void OversetPerformanceMonitor::gatherGlobalMetrics()
    {
#if defined(_BaseParallelMPI_)
        if (MPIInitialized())
        {
            // 收集搜索性能指标
            SearchMetrics globalSearchMetrics;

            // 汇总基本统计
            SumAllProcessor(searchMetrics.totalSearches, 0);
            SumAllProcessor(searchMetrics.successfulSearches, 0);
            SumAllProcessor(searchMetrics.failedSearches, 0);
            SumAllProcessor(searchMetrics.totalSearchTime, 0);

            // 获取最大值
            MaxAllProcessor(searchMetrics.maxSearchTime, 0);

            // 重新计算平均值
            if (GetMPIRank() == 0 && searchMetrics.totalSearches > 0)
            {
                searchMetrics.averageSearchTime = searchMetrics.totalSearchTime / searchMetrics.totalSearches;
            }

            // 广播结果到所有进程
            MPIBroadcast(searchMetrics.totalSearches, 0);
            MPIBroadcast(searchMetrics.successfulSearches, 0);
            MPIBroadcast(searchMetrics.failedSearches, 0);
            MPIBroadcast(searchMetrics.totalSearchTime, 0);
            MPIBroadcast(searchMetrics.averageSearchTime, 0);
            MPIBroadcast(searchMetrics.maxSearchTime, 0);

            // 收集通信性能指标
            SumAllProcessor(commMetrics.totalMessages, 0);
            SumAllProcessor(commMetrics.totalDataTransferred, 0);
            SumAllProcessor(commMetrics.totalCommTime, 0);
            MaxAllProcessor(commMetrics.maxCommTime, 0);

            if (GetMPIRank() == 0 && commMetrics.totalMessages > 0)
            {
                commMetrics.averageCommTime = commMetrics.totalCommTime / commMetrics.totalMessages;
            }

            MPIBroadcast(commMetrics.totalMessages, 0);
            MPIBroadcast(commMetrics.totalDataTransferred, 0);
            MPIBroadcast(commMetrics.totalCommTime, 0);
            MPIBroadcast(commMetrics.averageCommTime, 0);
            MPIBroadcast(commMetrics.maxCommTime, 0);

            // 收集内存使用指标
            SumAllProcessor(memoryMetrics.totalAllocations, 0);
            MaxAllProcessor(memoryMetrics.peakMemoryUsage, 0);
            SumAllProcessor(memoryMetrics.currentMemoryUsage, 0);

            MPIBroadcast(memoryMetrics.totalAllocations, 0);
            MPIBroadcast(memoryMetrics.peakMemoryUsage, 0);
            MPIBroadcast(memoryMetrics.currentMemoryUsage, 0);
        }
#endif
    }

    std::vector<OversetPerformanceMonitor::PerformanceBottleneck> OversetPerformanceMonitor::analyzeBottlenecks() const
    {
        std::vector<PerformanceBottleneck> bottlenecks;

        // 分析搜索成功率
        double successRate = searchMetrics.getSuccessRate();
        if (successRate < 0.95 && searchMetrics.totalSearches > 100)
        {
            PerformanceBottleneck bottleneck;
            bottleneck.category = "搜索性能";
            bottleneck.description = "供体搜索成功率较低 (" + std::to_string(successRate * 100.0) + "%)";
            bottleneck.severity = 1.0 - successRate;
            bottleneck.recommendation = "检查网格质量、重叠区域设置或增加搜索容差";
            bottlenecks.push_back(bottleneck);
        }

        // 分析搜索时间
        if (searchMetrics.averageSearchTime > 10.0 && searchMetrics.totalSearches > 100)
        {
            PerformanceBottleneck bottleneck;
            bottleneck.category = "搜索性能";
            bottleneck.description = "平均搜索时间过长 (" + std::to_string(searchMetrics.averageSearchTime) + " ms)";
            bottleneck.severity = std::min(1.0, searchMetrics.averageSearchTime / 50.0);
            bottleneck.recommendation = "优化KDT树构建、减少搜索范围或使用并行搜索";
            bottlenecks.push_back(bottleneck);
        }

        // 分析通信开销
        if (commMetrics.totalCommTime > 0.0 && searchMetrics.totalSearchTime > 0.0)
        {
            double commRatio = commMetrics.totalCommTime / (commMetrics.totalCommTime + searchMetrics.totalSearchTime);
            if (commRatio > 0.3)
            {
                PerformanceBottleneck bottleneck;
                bottleneck.category = "通信性能";
                bottleneck.description = "通信开销占比过高 (" + std::to_string(commRatio * 100.0) + "%)";
                bottleneck.severity = commRatio;
                bottleneck.recommendation = "优化数据分布、减少通信频率或使用异步通信";
                bottlenecks.push_back(bottleneck);
            }
        }

        // 分析内存使用
        if (memoryMetrics.peakMemoryUsage > 0)
        {
            double memoryGB = memoryMetrics.peakMemoryUsage / (1024.0 * 1024.0 * 1024.0);
            if (memoryGB > 8.0) // 假设8GB为警告阈值
            {
                PerformanceBottleneck bottleneck;
                bottleneck.category = "内存使用";
                bottleneck.description = "峰值内存使用过高 (" + std::to_string(memoryGB) + " GB)";
                bottleneck.severity = std::min(1.0, memoryGB / 32.0);
                bottleneck.recommendation = "优化数据结构、使用内存池或增加并行度";
                bottlenecks.push_back(bottleneck);
            }
        }

        return bottlenecks;
    }

    bool OversetPerformanceMonitor::shouldSample() const
    {
        if (samplingRate >= 1.0)
            return true;
        if (samplingRate <= 0.0)
            return false;

        // 简单的随机采样
        static thread_local std::random_device rd;
        static thread_local std::mt19937 gen(rd());
        static thread_local std::uniform_real_distribution<> dis(0.0, 1.0);

        return dis(gen) < samplingRate;
    }

    std::string OversetPerformanceMonitor::getCurrentTimeString() const
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);

        std::ostringstream oss;
        oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }

    void OversetPerformanceMonitor::writeCSVHeader(std::ofstream &file) const
    {
        file << "Metric,Category,Value,Unit" << std::endl;
    }

    void OversetPerformanceMonitor::writeCSVData(std::ofstream &file) const
    {
        // 搜索性能数据
        file << "TotalSearches,Search," << searchMetrics.totalSearches << ",count" << std::endl;
        file << "SuccessfulSearches,Search," << searchMetrics.successfulSearches << ",count" << std::endl;
        file << "FailedSearches,Search," << searchMetrics.failedSearches << ",count" << std::endl;
        file << "SuccessRate,Search," << std::fixed << std::setprecision(4) << searchMetrics.getSuccessRate() << ",ratio" << std::endl;
        file << "TotalSearchTime,Search," << std::fixed << std::setprecision(3) << searchMetrics.totalSearchTime << ",ms" << std::endl;
        file << "AverageSearchTime,Search," << std::fixed << std::setprecision(3) << searchMetrics.averageSearchTime << ",ms" << std::endl;
        file << "MaxSearchTime,Search," << std::fixed << std::setprecision(3) << searchMetrics.maxSearchTime << ",ms" << std::endl;

        // 通信性能数据
        file << "TotalMessages,Communication," << commMetrics.totalMessages << ",count" << std::endl;
        file << "TotalDataTransferred,Communication," << commMetrics.totalDataTransferred << ",bytes" << std::endl;
        file << "TotalCommTime,Communication," << std::fixed << std::setprecision(3) << commMetrics.totalCommTime << ",ms" << std::endl;
        file << "AverageCommTime,Communication," << std::fixed << std::setprecision(3) << commMetrics.averageCommTime << ",ms" << std::endl;
        file << "MaxCommTime,Communication," << std::fixed << std::setprecision(3) << commMetrics.maxCommTime << ",ms" << std::endl;

        // 内存使用数据
        file << "CurrentMemoryUsage,Memory," << memoryMetrics.currentMemoryUsage << ",bytes" << std::endl;
        file << "PeakMemoryUsage,Memory," << memoryMetrics.peakMemoryUsage << ",bytes" << std::endl;
        file << "TotalAllocations,Memory," << memoryMetrics.totalAllocations << ",count" << std::endl;
        file << "AverageMemoryUsage,Memory," << std::fixed << std::setprecision(0) << memoryMetrics.averageMemoryUsage << ",bytes" << std::endl;

        // 分区域搜索统计
        for (const auto &pair : searchMetrics.zoneSearchCounts)
        {
            int zoneID = pair.first;
            size_t searchCount = pair.second;
            file << "Zone" << zoneID << "Searches,ZoneSearch," << searchCount << ",count" << std::endl;

            auto it = searchMetrics.zoneSuccessCounts.find(zoneID);
            if (it != searchMetrics.zoneSuccessCounts.end())
            {
                file << "Zone" << zoneID << "Successes,ZoneSearch," << it->second << ",count" << std::endl;
                double successRate = searchCount > 0 ? (double)it->second / searchCount : 0.0;
                file << "Zone" << zoneID << "SuccessRate,ZoneSearch," << std::fixed << std::setprecision(4) << successRate << ",ratio" << std::endl;
            }
        }

        // 自定义事件数据
        for (const auto &categoryPair : customEvents)
        {
            const std::string &category = categoryPair.first;
            for (const auto &eventPair : categoryPair.second)
            {
                const std::string &eventName = eventPair.first;
                const std::vector<double> &values = eventPair.second;

                if (!values.empty())
                {
                    double sum = std::accumulate(values.begin(), values.end(), 0.0);
                    double avg = sum / values.size();
                    double maxVal = *std::max_element(values.begin(), values.end());
                    double minVal = *std::min_element(values.begin(), values.end());

                    file << eventName << "Count," << category << "," << values.size() << ",count" << std::endl;
                    file << eventName << "Average," << category << "," << std::fixed << std::setprecision(3) << avg << ",value" << std::endl;
                    file << eventName << "Max," << category << "," << std::fixed << std::setprecision(3) << maxVal << ",value" << std::endl;
                    file << eventName << "Min," << category << "," << std::fixed << std::setprecision(3) << minVal << ",value" << std::endl;
                }
            }
        }
    }

    std::string OversetPerformanceMonitor::generateJSONReport() const
    {
        std::ostringstream json;
        json << "{\n";
        json << "  \"timestamp\": \"" << getCurrentTimeString() << "\",\n";
        json << "  \"mpi_rank\": " << GetMPIRank() << ",\n";
        json << "  \"mpi_size\": " << GetMPISize() << ",\n";

        // 搜索性能指标
        json << "  \"search_metrics\": {\n";
        json << "    \"total_searches\": " << searchMetrics.totalSearches << ",\n";
        json << "    \"successful_searches\": " << searchMetrics.successfulSearches << ",\n";
        json << "    \"failed_searches\": " << searchMetrics.failedSearches << ",\n";
        json << "    \"success_rate\": " << std::fixed << std::setprecision(4) << searchMetrics.getSuccessRate() << ",\n";
        json << "    \"total_search_time_ms\": " << std::fixed << std::setprecision(3) << searchMetrics.totalSearchTime << ",\n";
        json << "    \"average_search_time_ms\": " << std::fixed << std::setprecision(3) << searchMetrics.averageSearchTime << ",\n";
        json << "    \"max_search_time_ms\": " << std::fixed << std::setprecision(3) << searchMetrics.maxSearchTime << ",\n";

        // 分区域统计
        json << "    \"zone_statistics\": {\n";
        bool first = true;
        for (const auto &pair : searchMetrics.zoneSearchCounts)
        {
            if (!first)
                json << ",\n";
            first = false;

            int zoneID = pair.first;
            size_t searchCount = pair.second;
            size_t successCount = 0;
            auto it = searchMetrics.zoneSuccessCounts.find(zoneID);
            if (it != searchMetrics.zoneSuccessCounts.end())
            {
                successCount = it->second;
            }

            double successRate = searchCount > 0 ? (double)successCount / searchCount : 0.0;
            json << "      \"zone_" << zoneID << "\": {\n";
            json << "        \"searches\": " << searchCount << ",\n";
            json << "        \"successes\": " << successCount << ",\n";
            json << "        \"success_rate\": " << std::fixed << std::setprecision(4) << successRate << "\n";
            json << "      }";
        }
        json << "\n    }\n";
        json << "  },\n";

        // 通信性能指标
        json << "  \"communication_metrics\": {\n";
        json << "    \"total_messages\": " << commMetrics.totalMessages << ",\n";
        json << "    \"total_data_transferred_bytes\": " << commMetrics.totalDataTransferred << ",\n";
        json << "    \"total_comm_time_ms\": " << std::fixed << std::setprecision(3) << commMetrics.totalCommTime << ",\n";
        json << "    \"average_comm_time_ms\": " << std::fixed << std::setprecision(3) << commMetrics.averageCommTime << ",\n";
        json << "    \"max_comm_time_ms\": " << std::fixed << std::setprecision(3) << commMetrics.maxCommTime;

        if (commMetrics.totalCommTime > 0.0)
        {
            double bandwidth = (double)commMetrics.totalDataTransferred / (commMetrics.totalCommTime / 1000.0) / (1024.0 * 1024.0);
            json << ",\n    \"average_bandwidth_mbps\": " << std::fixed << std::setprecision(2) << bandwidth;
        }
        json << "\n  },\n";

        // 内存使用指标
        json << "  \"memory_metrics\": {\n";
        json << "    \"current_memory_usage_bytes\": " << memoryMetrics.currentMemoryUsage << ",\n";
        json << "    \"peak_memory_usage_bytes\": " << memoryMetrics.peakMemoryUsage << ",\n";
        json << "    \"total_allocations\": " << memoryMetrics.totalAllocations << ",\n";
        json << "    \"average_memory_usage_bytes\": " << std::fixed << std::setprecision(0) << memoryMetrics.averageMemoryUsage << "\n";
        json << "  }";

        // 性能瓶颈分析
        auto bottlenecks = analyzeBottlenecks();
        if (!bottlenecks.empty())
        {
            json << ",\n  \"performance_bottlenecks\": [\n";
            for (size_t i = 0; i < bottlenecks.size(); ++i)
            {
                if (i > 0)
                    json << ",\n";
                const auto &bottleneck = bottlenecks[i];
                json << "    {\n";
                json << "      \"category\": \"" << bottleneck.category << "\",\n";
                json << "      \"description\": \"" << bottleneck.description << "\",\n";
                json << "      \"severity\": " << std::fixed << std::setprecision(3) << bottleneck.severity << ",\n";
                json << "      \"recommendation\": \"" << bottleneck.recommendation << "\"\n";
                json << "    }";
            }
            json << "\n  ]";
        }

        json << "\n}\n";
        return json.str();
    }

} // namespace Floga
