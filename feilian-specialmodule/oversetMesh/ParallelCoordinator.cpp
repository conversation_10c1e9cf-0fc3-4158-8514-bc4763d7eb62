#include "feilian-specialmodule/oversetMesh/ParallelCoordinator.h"

namespace Floga
{
    // ==================== 构造函数和析构函数 ====================

    ParallelCoordinator::ParallelCoordinator(
        ZoneManager *zoneManager,
        Mesh *localMesh,
        OversetPerformanceMonitor *performanceMonitor)
        : zoneManager(zoneManager),
          localMesh(localMesh),
          perfMonitor(performanceMonitor)
    {
        processorID = GetMPIRank();
        nProcessor = GetMPIRank();
        dim = localMesh->GetMeshDimension();

        // 预分配异步通信请求容器
        pendingRequests.reserve(nProcessor);

        if (perfMonitor)
        {
            perfMonitor->recordEvent("OversetParallelCoordinator", "Initialized",
                                     static_cast<double>(nProcessor));
        }
    }

    // ==================== 核心并行搜索协调 ====================

    void ParallelCoordinator::executeParallelSearch(
        List<List<Acceptor>> &groupedAcpts,
        Set<Acceptor> &searchResults,
        const SearchFunction &searchFunction)
    {
        auto startTime = std::chrono::high_resolution_clock::now();

        if (perfMonitor)
        {
            perfMonitor->recordEvent("ParallelSearch", "Started",
                                     static_cast<double>(groupedAcpts.size()));
        }

        try
        {
            // 1. 发送搜索请求到其他进程
            sendSearchRequests(groupedAcpts);

            // 2. 处理本地搜索和接收到的远程请求
            processIncomingRequests(searchFunction);

            // 3. 收集所有进程的搜索结果
            collectSearchResults(searchResults, groupedAcpts);

            // 4. 等待所有异步通信完成
            waitForAllRequests();
        }
        catch (const std::exception &e)
        {
            if (perfMonitor)
            {
                perfMonitor->recordEvent("ParallelSearch", "Error", 0.0);
            }
            throw;
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        if (perfMonitor)
        {
            perfMonitor->recordEvent("ParallelSearch", "Completed",
                                     static_cast<double>(duration.count()));
        }
    }

    // ==================== 分组策略实现 ====================

    void OversetParallelCoordinator::groupAcceptors(
        Set<int> &searchElemID,
        List<List<Acceptor>> &groupedAcpts,
        GroupingStrategy strategy,
        const std::vector<TreeInfo> &globalTreeInfo)
    {
        // 初始化分组容器
        groupedAcpts.clear();
        groupedAcpts.resize(nProcessor);

        if (searchElemID.empty())
        {
            return;
        }

        auto startTime = std::chrono::high_resolution_clock::now();

        // 根据策略选择分组方法
        switch (strategy)
        {
        case GroupingStrategy::BASIC:
            basicGrouping(searchElemID, groupedAcpts, globalTreeInfo);
            break;
        case GroupingStrategy::SMART:
            smartGrouping(searchElemID, groupedAcpts, globalTreeInfo);
            break;
        case GroupingStrategy::DYNAMIC:
            dynamicGrouping(searchElemID, groupedAcpts, globalTreeInfo);
            break;
        default:
            basicGrouping(searchElemID, groupedAcpts, globalTreeInfo);
            break;
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);

        if (perfMonitor)
        {
            perfMonitor->recordEvent("AcceptorGrouping", "Completed",
                                     static_cast<double>(duration.count()));
        }
    }

    void OversetParallelCoordinator::groupAcceptorsByDonor(
        const Set<Acceptor> &srcAcpts,
        List<List<Acceptor>> &groupedAcpts)
    {
        // 初始化分组容器
        groupedAcpts.clear();
        groupedAcpts.resize(nProcessor);

        if (srcAcpts.empty())
        {
            return;
        }

        // 按贡献单元的进程号分组
        for (const auto &acceptor : srcAcpts)
        {
            int donorProcID = acceptor.GetCentralDonorProcID();

            // 确保进程ID有效
            if (donorProcID >= 0 && donorProcID < nProcessor)
            {
                groupedAcpts[donorProcID].push_back(acceptor);
            }
            else
            {
                // 无效的进程ID，分配到当前进程
                groupedAcpts[processorID].push_back(acceptor);
            }
        }

        if (perfMonitor)
        {
            perfMonitor->recordEvent("AcceptorGroupingByDonor", "Completed",
                                     static_cast<double>(srcAcpts.size()));
        }
    }

    // ==================== 基础分组策略 ====================

    void OversetParallelCoordinator::basicGrouping(
        Set<int> &searchElemID,
        List<List<Acceptor>> &groupedAcpts,
        const std::vector<TreeInfo> &globalTreeInfo)
    {
        // 简单轮询分组策略
        int targetProc = 0;

        for (int elemID : searchElemID)
        {
            // 获取单元中心坐标
            Vector elemCenter = localMesh->GetElementCenter(elemID);

            // 创建Acceptor对象
            Acceptor acceptor(elemID, 0, 1, elemCenter);

            // 轮询分配到不同进程
            groupedAcpts[targetProc].push_back(acceptor);
            targetProc = (targetProc + 1) % nProcessor;
        }
    }

    void OversetParallelCoordinator::smartGrouping(
        Set<int> &searchElemID,
        List<List<Acceptor>> &groupedAcpts,
        const std::vector<TreeInfo> &globalTreeInfo)
    {
        // 智能分组策略：基于空间位置和负载均衡

        // 统计每个进程的负载
        std::vector<int> processLoad(nProcessor, 0);

        for (int elemID : searchElemID)
        {
            Vector elemCenter = localMesh->GetElementCenter(elemID);
            Acceptor acceptor(elemID, 0, 1, elemCenter);

            // 找到最可能包含该点的进程
            int bestProc = findBestProcessForElement(elemCenter, globalTreeInfo);

            // 考虑负载均衡
            if (processLoad[bestProc] > processLoad[processorID] + 10)
            {
                // 如果目标进程负载过重，分配到当前进程
                bestProc = processorID;
            }

            groupedAcpts[bestProc].push_back(acceptor);
            processLoad[bestProc]++;
        }
    }

    void OversetParallelCoordinator::dynamicGrouping(
        Set<int> &searchElemID,
        List<List<Acceptor>> &groupedAcpts,
        const std::vector<TreeInfo> &globalTreeInfo)
    {
        // 动态分组策略：适用于动态网格
        // 考虑历史搜索成功率和网格运动

        // 如果没有历史数据，回退到智能分组
        smartGrouping(searchElemID, groupedAcpts, globalTreeInfo);

        // TODO: 在未来版本中可以添加基于历史数据的优化
    }

    // ==================== 私有辅助方法 ====================

    bool OversetParallelCoordinator::elemCenterInTree(int elemID, const TreeInfo &treeInfo) const
    {
        Vector elemCenter = localMesh->GetElementCenter(elemID);

        // 检查点是否在树的边界框内
        for (int d = 0; d < dim; ++d)
        {
            if (elemCenter[d] < treeInfo.minBounds[d] ||
                elemCenter[d] > treeInfo.maxBounds[d])
            {
                return false;
            }
        }

        return true;
    }

    int OversetParallelCoordinator::findBestProcessForElement(
        const Vector &elemCenter,
        const std::vector<TreeInfo> &globalTreeInfo) const
    {
        int bestProc = processorID;
        double minDistance = std::numeric_limits<double>::max();

        for (int proc = 0; proc < nProcessor && proc < globalTreeInfo.size(); ++proc)
        {
            const TreeInfo &treeInfo = globalTreeInfo[proc];

            // 计算点到树边界框中心的距离
            Vector treeCenter;
            for (int d = 0; d < dim; ++d)
            {
                treeCenter[d] = (treeInfo.minBounds[d] + treeInfo.maxBounds[d]) * 0.5;
            }

            double distance = (elemCenter - treeCenter).magnitude();

            if (distance < minDistance)
            {
                minDistance = distance;
                bestProc = proc;
            }
        }

        return bestProc;
    }

    void OversetParallelCoordinator::waitForAllRequests()
    {
        // 等待所有异步MPI请求完成
        if (!pendingRequests.empty())
        {
#if defined(_BaseParallelMPI_)
            boost::mpi::wait_all(pendingRequests.begin(), pendingRequests.end());
#endif
            pendingRequests.clear();
        }
    }

    // ==================== MPI通信实现 ====================

    void OversetParallelCoordinator::sendSearchRequests(const List<List<Acceptor>> &groupedAcpts)
    {
#if defined(_BaseParallelMPI_)
        pendingRequests.clear();

        for (int targetProc = 0; targetProc < nProcessor; ++targetProc)
        {
            if (targetProc != processorID && !groupedAcpts[targetProc].empty())
            {
                // 异步发送搜索请求到目标进程
                MPIRequest request = mpi_world.isend(targetProc, 0, groupedAcpts[targetProc]);
                pendingRequests.push_back(request);

                if (perfMonitor)
                {
                    perfMonitor->recordEvent("MPI_Send", "SearchRequest",
                                             static_cast<double>(groupedAcpts[targetProc].size()));
                }
            }
        }
#endif
    }

    void OversetParallelCoordinator::processIncomingRequests(const SearchFunction &searchFunction)
    {
#if defined(_BaseParallelMPI_)
        // 处理本地搜索请求
        List<Acceptor> localAcceptors;

        // 收集所有发送给当前进程的搜索请求
        for (int sourceProc = 0; sourceProc < nProcessor; ++sourceProc)
        {
            if (sourceProc != processorID)
            {
                // 检查是否有来自该进程的消息
                boost::optional<boost::mpi::status> status = mpi_world.iprobe(sourceProc, 0);

                if (status)
                {
                    List<Acceptor> receivedAcceptors;
                    mpi_world.recv(sourceProc, 0, receivedAcceptors);

                    // 合并到本地搜索列表
                    localAcceptors.insert(localAcceptors.end(),
                                          receivedAcceptors.begin(),
                                          receivedAcceptors.end());

                    if (perfMonitor)
                    {
                        perfMonitor->recordEvent("MPI_Recv", "SearchRequest",
                                                 static_cast<double>(receivedAcceptors.size()));
                    }
                }
            }
        }

        // 执行本地搜索
        if (!localAcceptors.empty())
        {
            searchFunction(localAcceptors);
        }
#endif
    }

    void OversetParallelCoordinator::collectSearchResults(
        Set<Acceptor> &searchResults,
        const List<List<Acceptor>> &groupedAcpts)
    {
#if defined(_BaseParallelMPI_)
        searchResults.clear();

        // 收集所有进程的搜索结果
        List<List<Acceptor>> allResults;
        boost::mpi::all_gather(mpi_world, groupedAcpts, allResults);

        // 合并结果并去重
        for (const auto &procResults : allResults)
        {
            for (const auto &acceptorList : procResults)
            {
                for (const auto &acceptor : acceptorList)
                {
                    // 只保留找到有效贡献单元的结果
                    if (acceptor.GetCentralDonorID() >= 0)
                    {
                        searchResults.insert(acceptor);
                    }
                }
            }
        }

        if (perfMonitor)
        {
            perfMonitor->recordEvent("ResultCollection", "Completed",
                                     static_cast<double>(searchResults.size()));
        }
#endif
    }

} // namespace Floga
