////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetPerformanceMonitor_Usage_Example.cpp
//! <AUTHOR>
//! @brief OversetPerformanceMonitor使用示例
//! @date 2024-03-12
//------------------------------------------------------------------------------

#include "OversetPerformanceMonitor.h"
#include "basic/common/SystemControl.h"
#include "basic/common/MPI.h"

namespace Floga
{
    /**
     * @brief 示例：在DonorSearcher中集成性能监控
     */
    class ExampleDonorSearcher
    {
    private:
        std::unique_ptr<OversetPerformanceMonitor> performanceMonitor;
        
    public:
        ExampleDonorSearcher()
        {
            performanceMonitor = std::make_unique<OversetPerformanceMonitor>();
            
            // 配置性能监控
            performanceMonitor->setDetailedMonitoring(true);
            performanceMonitor->setSamplingRate(1.0); // 100%采样，生产环境可调低
        }
        
        /**
         * @brief 执行供体搜索（示例）
         */
        bool performDonorSearch(int acceptorID, int targetZoneID)
        {
            // 记录搜索开始时间
            auto searchStartTime = performanceMonitor->recordSearchStart();
            
            // 记录内存使用（可选）
            size_t currentMemory = getCurrentMemoryUsage(); // 假设的函数
            performanceMonitor->recordMemoryUsage(currentMemory);
            
            // 模拟实际的搜索过程
            bool searchResult = false;
            try
            {
                // 1. KDT树搜索
                auto kdtStartTime = std::chrono::high_resolution_clock::now();
                // ... 实际的KDT搜索代码 ...
                auto kdtEndTime = std::chrono::high_resolution_clock::now();
                
                auto kdtDuration = std::chrono::duration_cast<std::chrono::microseconds>(kdtEndTime - kdtStartTime);
                performanceMonitor->recordEvent("KDTSearch", "SearchTime", kdtDuration.count() / 1000.0);
                
                // 2. 几何验证
                auto geomStartTime = std::chrono::high_resolution_clock::now();
                // ... 实际的几何验证代码 ...
                auto geomEndTime = std::chrono::high_resolution_clock::now();
                
                auto geomDuration = std::chrono::duration_cast<std::chrono::microseconds>(geomEndTime - geomStartTime);
                performanceMonitor->recordEvent("GeometryValidation", "ValidationTime", geomDuration.count() / 1000.0);
                
                searchResult = true; // 假设搜索成功
            }
            catch (const std::exception& e)
            {
                searchResult = false;
                performanceMonitor->recordEvent("SearchErrors", "Exception", 1.0);
            }
            
            // 记录搜索结束
            performanceMonitor->recordSearchEnd(searchStartTime, searchResult, targetZoneID);
            
            return searchResult;
        }
        
        /**
         * @brief 执行并行通信（示例）
         */
        void performParallelCommunication(const std::vector<int>& targetProcesses, 
                                        const std::vector<char>& data)
        {
            auto commStartTime = performanceMonitor->recordCommStart();
            
            try
            {
                // 模拟MPI通信
                for (int targetProc : targetProcesses)
                {
                    // ... 实际的MPI发送代码 ...
                    // MPI_Send(data.data(), data.size(), MPI_CHAR, targetProc, 0, MPI_COMM_WORLD);
                }
                
                // 记录通信结束
                performanceMonitor->recordCommEnd(commStartTime, data.size() * targetProcesses.size());
            }
            catch (const std::exception& e)
            {
                performanceMonitor->recordCommEnd(commStartTime, 0);
                performanceMonitor->recordEvent("CommunicationErrors", "Exception", 1.0);
            }
        }
        
        /**
         * @brief 获取性能监控器（用于外部访问）
         */
        OversetPerformanceMonitor* getPerformanceMonitor() const
        {
            return performanceMonitor.get();
        }
        
    private:
        /**
         * @brief 获取当前内存使用量（示例实现）
         */
        size_t getCurrentMemoryUsage() const
        {
            // 这里应该实现实际的内存使用量获取
            // 可以使用系统调用或第三方库
            return 1024 * 1024 * 100; // 示例：100MB
        }
    };
    
    /**
     * @brief 示例：在OversetMesh中集成性能监控
     */
    class ExampleOversetMesh
    {
    private:
        std::unique_ptr<OversetPerformanceMonitor> performanceMonitor;
        std::unique_ptr<ExampleDonorSearcher> donorSearcher;
        
    public:
        ExampleOversetMesh()
        {
            performanceMonitor = std::make_unique<OversetPerformanceMonitor>();
            donorSearcher = std::make_unique<ExampleDonorSearcher>();
        }
        
        /**
         * @brief 执行重叠网格装配
         */
        void performOversetGridAssembly()
        {
            if (GetMPIRank() == 0)
            {
                Print("开始重叠网格装配...");
            }
            
            auto assemblyStartTime = std::chrono::high_resolution_clock::now();
            
            // 1. 壁面距离计算
            performWallDistanceCalculation();
            
            // 2. 供体搜索
            performDonorSearch();
            
            // 3. 插值权重计算
            performInterpolationWeightCalculation();
            
            // 4. 并行数据交换
            performParallelDataExchange();
            
            auto assemblyEndTime = std::chrono::high_resolution_clock::now();
            auto assemblyDuration = std::chrono::duration_cast<std::chrono::milliseconds>(assemblyEndTime - assemblyStartTime);
            
            performanceMonitor->recordEvent("OversetAssembly", "TotalTime", assemblyDuration.count());
            
            // 收集全局性能数据
            performanceMonitor->gatherGlobalMetrics();
            
            // 生成性能报告
            if (GetMPIRank() == 0)
            {
                performanceMonitor->printPerformanceReport();
                
                // 分析性能瓶颈
                auto bottlenecks = performanceMonitor->analyzeBottlenecks();
                if (!bottlenecks.empty())
                {
                    Print("检测到性能瓶颈:");
                    for (const auto& bottleneck : bottlenecks)
                    {
                        Print("  " + bottleneck.category + ": " + bottleneck.description);
                        Print("    建议: " + bottleneck.recommendation);
                    }
                }
                
                // 导出详细数据
                performanceMonitor->exportMetricsToCSV("overset_assembly_performance.csv");
                performanceMonitor->exportMetricsToJSON("overset_assembly_performance.json");
            }
        }
        
    private:
        void performWallDistanceCalculation()
        {
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // ... 实际的壁面距离计算代码 ...
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            performanceMonitor->recordEvent("WallDistance", "CalculationTime", duration.count());
        }
        
        void performDonorSearch()
        {
            // 使用DonorSearcher进行搜索
            for (int i = 0; i < 1000; ++i) // 示例：1000个接受单元
            {
                int zoneID = i % 3; // 3个区域
                donorSearcher->performDonorSearch(i, zoneID);
            }
        }
        
        void performInterpolationWeightCalculation()
        {
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // ... 实际的插值权重计算代码 ...
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            performanceMonitor->recordEvent("Interpolation", "WeightCalculationTime", duration.count());
        }
        
        void performParallelDataExchange()
        {
            std::vector<int> targetProcesses = {1, 2, 3}; // 示例目标进程
            std::vector<char> data(1024 * 1024, 'A'); // 1MB数据
            
            donorSearcher->performParallelCommunication(targetProcesses, data);
        }
    };
}

/**
 * @brief 主函数示例
 */
int main(int argc, char** argv)
{
    using namespace Floga;
    
    // 初始化MPI
    InitializeMPI(argc, argv);
    SetInfoFile("overset_performance_example.info");
    
    if (GetMPIRank() == 0)
    {
        PrintTitleInfo(" 重叠网格性能监控集成示例 ");
    }
    
    try
    {
        // 创建重叠网格对象
        ExampleOversetMesh oversetMesh;
        
        // 执行重叠网格装配（包含性能监控）
        oversetMesh.performOversetGridAssembly();
        
        if (GetMPIRank() == 0)
        {
            Print("重叠网格装配完成，性能数据已保存");
        }
    }
    catch (const std::exception& e)
    {
        FatalError("执行过程中发生异常: " + std::string(e.what()));
    }
    
    CloseInfoFile();
    return 0;
}
