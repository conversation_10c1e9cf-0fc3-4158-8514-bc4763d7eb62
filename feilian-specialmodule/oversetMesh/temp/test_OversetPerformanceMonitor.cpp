////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file test_OversetPerformanceMonitor.cpp
//! <AUTHOR>
//! @brief OversetPerformanceMonitor测试程序
//! @date 2024-03-12
//------------------------------------------------------------------------------

#include "OversetPerformanceMonitor.h"
#include "basic/common/SystemControl.h"
#include "basic/common/MPI.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace Floga;

void testBasicFunctionality()
{
    Print("=== 测试基本功能 ===");
    
    OversetPerformanceMonitor monitor;
    
    // 测试搜索性能监控
    for (int i = 0; i < 100; ++i)
    {
        auto startTime = monitor.recordSearchStart();
        
        // 模拟搜索操作
        std::this_thread::sleep_for(std::chrono::microseconds(100 + i % 50));
        
        bool successful = (i % 10 != 0); // 90%成功率
        int zoneID = i % 3; // 3个区域
        
        monitor.recordSearchEnd(startTime, successful, zoneID);
    }
    
    // 测试通信性能监控
    for (int i = 0; i < 20; ++i)
    {
        auto startTime = monitor.recordCommStart();
        
        // 模拟通信操作
        std::this_thread::sleep_for(std::chrono::microseconds(200 + i % 100));
        
        size_t dataSize = 1024 * (i + 1);
        monitor.recordCommEnd(startTime, dataSize);
    }
    
    // 测试内存监控
    for (int i = 0; i < 50; ++i)
    {
        size_t memoryUsage = 1024 * 1024 * (10 + i); // 10MB起始，递增
        monitor.recordMemoryUsage(memoryUsage);
    }
    
    // 测试自定义事件
    for (int i = 0; i < 30; ++i)
    {
        monitor.recordEvent("KDTTree", "BuildTime", 5.0 + i * 0.1);
        monitor.recordEvent("WallDistance", "CalculationTime", 2.0 + i * 0.05);
    }
    
    Print("基本功能测试完成");
}

void testReportGeneration()
{
    Print("=== 测试报告生成 ===");
    
    OversetPerformanceMonitor monitor;
    
    // 添加一些测试数据
    for (int i = 0; i < 50; ++i)
    {
        auto startTime = monitor.recordSearchStart();
        std::this_thread::sleep_for(std::chrono::microseconds(50));
        monitor.recordSearchEnd(startTime, i % 5 != 0, i % 2);
        
        monitor.recordMemoryUsage(1024 * 1024 * (5 + i));
    }
    
    // 测试控制台报告
    monitor.printPerformanceReport();
    
    // 测试文件导出
    monitor.exportMetricsToFile("overset_performance_test.txt");
    monitor.exportMetricsToCSV("overset_performance_test.csv");
    monitor.exportMetricsToJSON("overset_performance_test.json");
    
    Print("报告生成测试完成");
}

void testBottleneckAnalysis()
{
    Print("=== 测试性能瓶颈分析 ===");
    
    OversetPerformanceMonitor monitor;
    
    // 创建一些性能问题的场景
    
    // 低成功率场景
    for (int i = 0; i < 100; ++i)
    {
        auto startTime = monitor.recordSearchStart();
        std::this_thread::sleep_for(std::chrono::microseconds(100));
        bool successful = (i % 3 == 0); // 33%成功率，较低
        monitor.recordSearchEnd(startTime, successful, 0);
    }
    
    // 高搜索时间场景
    for (int i = 0; i < 50; ++i)
    {
        auto startTime = monitor.recordSearchStart();
        std::this_thread::sleep_for(std::chrono::milliseconds(15)); // 15ms，较高
        monitor.recordSearchEnd(startTime, true, 1);
    }
    
    // 高通信开销场景
    for (int i = 0; i < 30; ++i)
    {
        auto startTime = monitor.recordCommStart();
        std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 20ms通信时间
        monitor.recordCommEnd(startTime, 1024 * 1024);
    }
    
    // 高内存使用场景
    monitor.recordMemoryUsage(12ULL * 1024 * 1024 * 1024); // 12GB
    
    // 分析瓶颈
    auto bottlenecks = monitor.analyzeBottlenecks();
    
    Print("发现 " + std::to_string(bottlenecks.size()) + " 个性能瓶颈:");
    for (const auto& bottleneck : bottlenecks)
    {
        Print("  类别: " + bottleneck.category);
        Print("  描述: " + bottleneck.description);
        Print("  严重程度: " + std::to_string(bottleneck.severity));
        Print("  建议: " + bottleneck.recommendation);
        Print("");
    }
    
    Print("性能瓶颈分析测试完成");
}

void testSamplingAndDetailedMonitoring()
{
    Print("=== 测试采样和详细监控 ===");
    
    OversetPerformanceMonitor monitor;
    
    // 测试采样率设置
    monitor.setSamplingRate(0.5); // 50%采样率
    Print("设置采样率为50%");
    
    // 启用详细监控
    monitor.setDetailedMonitoring(true);
    Print("启用详细监控模式");
    
    // 进行大量操作测试采样
    for (int i = 0; i < 200; ++i)
    {
        auto startTime = monitor.recordSearchStart();
        std::this_thread::sleep_for(std::chrono::microseconds(10));
        monitor.recordSearchEnd(startTime, true, 0);
        
        monitor.recordMemoryUsage(1024 * 1024 * (i + 1));
        monitor.recordEvent("TestCategory", "TestEvent", i * 0.1);
    }
    
    // 检查结果
    const auto& searchMetrics = monitor.getSearchMetrics();
    const auto& memoryMetrics = monitor.getMemoryMetrics();
    
    Print("实际记录的搜索次数: " + std::to_string(searchMetrics.totalSearches));
    Print("实际记录的内存分配次数: " + std::to_string(memoryMetrics.totalAllocations));
    Print("内存历史记录数量: " + std::to_string(memoryMetrics.memoryHistory.size()));
    
    // 重置指标测试
    monitor.resetAllMetrics();
    Print("重置所有指标");
    
    const auto& resetSearchMetrics = monitor.getSearchMetrics();
    Print("重置后搜索次数: " + std::to_string(resetSearchMetrics.totalSearches));
    
    Print("采样和详细监控测试完成");
}

int main(int argc, char** argv)
{
    // 初始化MPI环境（如果可用）
    InitializeMPI(argc, argv);
    
    // 设置信息输出
    SetInfoFile("overset_performance_monitor_test.info");
    
    if (GetMPIRank() == 0)
    {
        PrintTitleInfo(" OversetPerformanceMonitor 测试程序 ");
        PrintSystemTime();
    }
    
    try
    {
        testBasicFunctionality();
        testReportGeneration();
        testBottleneckAnalysis();
        testSamplingAndDetailedMonitoring();
        
        if (GetMPIRank() == 0)
        {
            Print("=== 所有测试完成 ===");
            Print("请检查生成的输出文件:");
            Print("  - overset_performance_test.txt");
            Print("  - overset_performance_test.csv");
            Print("  - overset_performance_test.json");
        }
    }
    catch (const std::exception& e)
    {
        FatalError("测试过程中发生异常: " + std::string(e.what()));
    }
    
    CloseInfoFile();
    return 0;
}
