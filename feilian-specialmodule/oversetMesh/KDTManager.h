////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file KDTManager.h
//! <AUTHOR>
//! @brief 重叠网格KDT管理器
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_KDTManager_
#define _specialModule_oversetMesh_KDTManager_

#include "feilian-specialmodule/oversetMesh/OverDefines.h"

namespace Floga
{
    /**
     * @brief KDT搜索器管理器
     *
     * 负责多子域KDT搜索器的创建、销毁、池化管理和生命周期控制。
     * 作为kdt_utilities模块的适配器，提供重叠网格特定的管理功能。
     */
    class KDTManager
    {
    public:
        /**
         * @brief 默认构造函数
         */
        KDTManager();

        /**
         * @brief 构造函数
         * @param dimension 网格维度
         * @param zoneManager 域管理器指针
         */
        KDTManager(int dimension, ZoneManager *zoneManager);

        /**
         * @brief 析构函数
         */
        ~KDTManager();

        /**
         * @brief 初始化管理器
         * @param dimension 网格维度
         * @param zoneManager 域管理器指针
         */
        void initialize(int dimension, ZoneManager *zoneManager);

        /**
         * @brief 初始化所有子域的KDT搜索器
         * @param localMesh 本地网格指针
         * @param nZones 子域数量
         */
        void initializeKDTSearchers(Mesh *localMesh, int nZones);

        /**
         * @brief 获取指定子域的KDT搜索器
         * @param zoneID 子域编号
         * @return KDT搜索器指针，如果不存在返回nullptr
         */
        KDT *getKDTSearcher(int zoneID) const;

        /**
         * @brief 清理所有KDT搜索器
         */
        void clear();

        /**
         * @brief 重建指定子域的KDT树
         * @param zoneID 子域编号
         * @param localMesh 本地网格指针
         * @return 是否成功重建
         */
        bool rebuildKDTTree(int zoneID, Mesh *localMesh);

        /**
         * @brief 增量更新指定子域的KDT树（用于动态网格）
         * @param zoneID 子域编号
         * @param localMesh 本地网格指针
         * @param displacementField 位移场
         * @return 是否成功更新
         */
        bool incrementalUpdateKDTTree(int zoneID, Mesh *localMesh,
                                      const std::vector<Vector> &displacementField);

        /**
         * @brief 获取全局树信息
         * @return 全局树信息向量
         */
        const std::vector<TreeInfo> &getGlobalTreeInfo() const;

        /**
         * @brief 强制更新全局树信息缓存
         */
        void updateGlobalTreeInfo();

        /**
         * @brief 获取KDT搜索器数量
         */
        size_t getSearcherCount() const { return kdtSearchers.size(); }

        /**
         * @brief 检查是否已初始化
         */
        bool isInitialized() const { return initialized; }

        /**
         * @brief 启用/禁用搜索器池化
         * @param enable 是否启用池化
         * @param poolSize 池大小（启用时）
         */
        void setPoolingEnabled(bool enable, size_t poolSize = 10);

        /**
         * @brief 获取池化统计信息
         */
        struct PoolingStats
        {
            size_t totalAcquires = 0;
            size_t totalReleases = 0;
            size_t poolHits = 0;
            size_t poolMisses = 0;
            size_t currentPoolSize = 0;
            size_t maxPoolSize = 0;
        };

        const PoolingStats &getPoolingStats() const { return poolingStats; }

    private:
        /**
         * @brief KDT搜索器池
         *
         * 提供KDT对象的池化管理，减少频繁创建/销毁的开销
         */
        class KDTSearcherPool
        {
        private:
            std::vector<std::unique_ptr<KDT>> pool;
            std::queue<KDT *> available;
            int dimension;
            size_t maxPoolSize;
            mutable PoolingStats stats;

        public:
            KDTSearcherPool(int dim, size_t maxSize = 10)
                : dimension(dim), maxPoolSize(maxSize) {}

            KDT *acquire();
            void release(KDT *searcher);
            void clear();
            void setMaxPoolSize(size_t maxSize) { maxPoolSize = maxSize; }
            const PoolingStats &getStats() const { return stats; }
        };

        /**
         * @brief 创建指定子域的网格
         * @param zoneID 子域编号
         * @param localMesh 本地网格指针
         * @return 子域网格指针
         */
        Mesh *createZoneMesh(int zoneID, Mesh *localMesh);

        /**
         * @brief 创建子域的KDT搜索器
         * @param zoneID 子域编号
         * @param zoneMesh 子域网格指针
         * @return KDT搜索器指针
         */
        KDT *createKDTSearcher(int zoneID, Mesh *zoneMesh);

        /**
         * @brief 检查子域是否需要重建KDT
         * @param zoneID 子域编号
         * @param localMesh 本地网格指针
         * @return 是否需要重建
         */
        bool shouldRebuildZone(int zoneID, Mesh *localMesh) const;

    private:
        bool initialized = false;                           // 是否已初始化
        int dimension = 3;                                  // 网格维度
        ZoneManager *zoneManager = nullptr;                 // 域管理器指针
        List<KDT *> kdtSearchers;                           // 各子域的KDT搜索器
        std::unique_ptr<KDTSearcherPool> kdtPool;           // KDT搜索器池
        mutable std::vector<TreeInfo> cachedGlobalTreeInfo; // 缓存的全局树信息
        mutable bool treeInfoNeedsUpdate = true;            // 树信息是否需要更新

        // 池化管理
        bool poolingEnabled = false;       // 是否启用池化
        mutable PoolingStats poolingStats; // 池化统计信息

        // 动态网格支持
        std::vector<bool> zoneNeedsRebuild;                            // 标记哪些子域需要重建
        std::chrono::high_resolution_clock::time_point lastUpdateTime; // 上次更新时间
    };
} // namespace Floga

#endif
