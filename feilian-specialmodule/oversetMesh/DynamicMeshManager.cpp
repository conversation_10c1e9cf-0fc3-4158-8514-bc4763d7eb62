#include "feilian-specialmodule/oversetMesh/DynamicMeshManager.h"

namespace Floga
{
    // ==================== 内部类实现 ====================

    /**
     * @brief 搜索结果缓存管理器
     */
    class DynamicMeshManager::SearchResultCache
    {
    public:
        struct CacheEntry
        {
            Acceptor result;
            Vector position;
            double timestamp;
            Scalar confidence;
        };

        bool tryGetResult(int acceptorID, const Vector &currentPos, Acceptor &result, double currentTime, double validityTime)
        {
            auto it = cache.find(acceptorID);
            if (it != cache.end())
            {
                const auto &entry = it->second;
                // 检查时间有效性和位置相似性
                if (currentTime - entry.timestamp <= validityTime &&
                    (currentPos - entry.position).Magnitude() < 0.01) // 位置容差
                {
                    result = entry.result;
                    return true;
                }
            }
            return false;
        }

        void cacheResult(int acceptorID, const Acceptor &result, const Vector &position,
                         double timestamp, Scalar confidence = 1.0)
        {
            cache[acceptorID] = {result, position, timestamp, confidence};
        }

        void clear() { cache.clear(); }

    private:
        std::unordered_map<int, CacheEntry> cache;
    };

    /**
     * @brief 时间步继承管理器
     */
    class OversetDynamicMeshManager::TimeStepInheritanceManager
    {
    public:
        bool tryInherit(int acceptorID, const Vector &currentPos, Acceptor &result)
        {
            auto it = previousResults.find(acceptorID);
            if (it != previousResults.end())
            {
                // 简单的位置预测：假设匀速运动
                result = it->second;
                return true;
            }
            return false;
        }

        void saveResults(const std::unordered_map<int, Acceptor> &results)
        {
            previousResults = results;
        }

        void clear() { previousResults.clear(); }

    private:
        std::unordered_map<int, Acceptor> previousResults;
    };

    /**
     * @brief 增量KDT管理器
     */
    class OversetDynamicMeshManager::IncrementalKDTManager
    {
    public:
        bool shouldRebuildZone(int zoneID, const std::vector<Scalar> &displacements,
                               Scalar threshold) const
        {
            // 计算该子域的平均位移
            Scalar avgDisplacement = 0.0;
            int count = 0;

            for (const auto &disp : displacements)
            {
                avgDisplacement += disp;
                count++;
            }

            if (count > 0)
            {
                avgDisplacement /= count;
                return avgDisplacement > threshold;
            }

            return false;
        }

        void markZoneForRebuild(int zoneID)
        {
            zonesToRebuild.insert(zoneID);
        }

        const std::set<int> &getZonesToRebuild() const
        {
            return zonesToRebuild;
        }

        void clearRebuildFlags()
        {
            zonesToRebuild.clear();
        }

    private:
        std::set<int> zonesToRebuild;
    };

    // ==================== 主类实现 ====================

    OversetDynamicMeshManager::OversetDynamicMeshManager(Scalar rebuildThreshold_, double cacheValidityTime_)
        : rebuildThreshold(rebuildThreshold_), cacheValidityTime(cacheValidityTime_)
    {
        resultCache = std::make_unique<SearchResultCache>();
        inheritanceManager = std::make_unique<TimeStepInheritanceManager>();
        kdtUpdateManager = std::make_unique<IncrementalKDTManager>();
    }

    void OversetDynamicMeshManager::enableDynamicMesh(int numElements)
    {
        isEnabled = true;
        previousPositions.resize(numElements);
        currentDisplacements.resize(numElements);
        elementDisplacements.resize(numElements);

        // 初始化位置为零向量
        std::fill(previousPositions.begin(), previousPositions.end(), Vector(0, 0, 0));
        std::fill(currentDisplacements.begin(), currentDisplacements.end(), Vector(0, 0, 0));
        std::fill(elementDisplacements.begin(), elementDisplacements.end(), 0.0);
    }

    void OversetDynamicMeshManager::disableDynamicMesh()
    {
        isEnabled = false;
        previousPositions.clear();
        currentDisplacements.clear();
        elementDisplacements.clear();

        if (resultCache)
            resultCache->clear();
        if (inheritanceManager)
            inheritanceManager->clear();
        if (kdtUpdateManager)
            kdtUpdateManager->clearRebuildFlags();
    }

    void OversetDynamicMeshManager::updateMeshMotion(const std::vector<Vector> &displacementField,
                                                     double timeStep, int stepNumber)
    {
        if (!isEnabled)
            return;

        currentTimeStep = timeStep;
        currentStepNumber = stepNumber;

        // 更新位移场
        size_t minSize = std::min(displacementField.size(), currentDisplacements.size());
        for (size_t i = 0; i < minSize; ++i)
        {
            currentDisplacements[i] = displacementField[i];
        }

        // 计算单元位移幅度
        calculateElementDisplacements(displacementField);

        // 更新统计信息
        if (!elementDisplacements.empty())
        {
            auto minmax = std::minmax_element(elementDisplacements.begin(), elementDisplacements.end());
            metrics.maxDisplacement = *minmax.second;

            Scalar sum = 0.0;
            for (const auto &disp : elementDisplacements)
            {
                sum += disp;
            }
            metrics.averageDisplacement = sum / elementDisplacements.size();
        }
    }

    bool OversetDynamicMeshManager::shouldRebuildKDT(int zoneID, int zoneStartID, int zoneElemNum) const
    {
        if (!isEnabled || kdtUpdateManager == nullptr)
            return true;

        // 提取该子域的位移数据
        std::vector<Scalar> zoneDisplacements;
        int endID = std::min(zoneStartID + zoneElemNum, static_cast<int>(elementDisplacements.size()));

        for (int i = zoneStartID; i < endID; ++i)
        {
            zoneDisplacements.push_back(elementDisplacements[i]);
        }

        return kdtUpdateManager->shouldRebuildZone(zoneID, zoneDisplacements, rebuildThreshold);
    }

    bool OversetDynamicMeshManager::tryGetCachedResult(int acceptorID, const Vector &currentPos, Acceptor &result)
    {
        if (!isEnabled || resultCache == nullptr)
            return false;

        bool found = resultCache->tryGetResult(acceptorID, currentPos, result,
                                               currentStepNumber * currentTimeStep, cacheValidityTime);
        if (found)
        {
            metrics.cacheHits++;
        }
        else
        {
            metrics.cacheMisses++;
        }

        return found;
    }

    void OversetDynamicMeshManager::cacheSearchResult(const Acceptor &result, const Vector &position, Scalar confidence)
    {
        if (!isEnabled || resultCache == nullptr)
            return;

        resultCache->cacheResult(result.GetAcceptorID(), result, position,
                                 currentStepNumber * currentTimeStep, confidence);
    }

    bool OversetDynamicMeshManager::tryInheritFromPreviousStep(int acceptorID, const Vector &currentPos, Acceptor &result)
    {
        if (!isEnabled || inheritanceManager == nullptr)
            return false;

        bool inherited = inheritanceManager->tryInherit(acceptorID, currentPos, result);
        if (inherited)
        {
            metrics.inheritanceHits++;
        }

        return inherited;
    }

    void OversetDynamicMeshManager::saveCurrentStepResults(const std::unordered_map<int, Acceptor> &results,
                                                           const std::vector<Vector> &positions)
    {
        if (!isEnabled || inheritanceManager == nullptr)
            return;

        inheritanceManager->saveResults(results);

        // 更新前一时间步位置
        size_t minSize = std::min(positions.size(), previousPositions.size());
        for (size_t i = 0; i < minSize; ++i)
        {
            previousPositions[i] = positions[i];
        }
    }

    OversetDynamicMeshManager::MotionType OversetDynamicMeshManager::analyzeMotionType(const std::vector<Vector> &displacements) const
    {
        if (displacements.empty())
            return MotionType::RIGID_BODY;

        // 简单的运动类型分析
        Vector avgDisplacement(0, 0, 0);
        for (const auto &disp : displacements)
        {
            avgDisplacement += disp;
        }
        avgDisplacement /= static_cast<Scalar>(displacements.size());

        // 计算位移的方差
        Scalar variance = 0.0;
        for (const auto &disp : displacements)
        {
            variance += (disp - avgDisplacement).MagnitudeSquared();
        }
        variance /= static_cast<Scalar>(displacements.size());

        // 根据方差判断运动类型
        if (variance < 1e-6)
        {
            return MotionType::RIGID_BODY;
        }
        else if (variance < 1e-3)
        {
            return MotionType::MIXED;
        }
        else
        {
            return MotionType::DEFORMATION;
        }
    }

    void OversetDynamicMeshManager::calculateElementDisplacements(const std::vector<Vector> &displacements)
    {
        size_t minSize = std::min(displacements.size(), elementDisplacements.size());
        for (size_t i = 0; i < minSize; ++i)
        {
            elementDisplacements[i] = displacements[i].Magnitude();
        }
    }

    Vector OversetDynamicMeshManager::calculateVelocity(int elemID) const
    {
        if (!isEnabled || currentTimeStep <= 0.0 ||
            elemID >= static_cast<int>(currentDisplacements.size()))
        {
            return Vector(0, 0, 0);
        }

        return currentDisplacements[elemID] / currentTimeStep;
    }
} // namespace Floga
