# Makefile for OversetPerformanceMonitor
# 重叠网格性能监控模块编译配置

# 编译器设置
CXX = mpicxx
CXXFLAGS = -std=c++11 -O2 -Wall -Wextra -g

# 包含路径
INCLUDES = -I../../ -I../../basic/common -I../../basic/parallel

# 库路径和链接库
LIBPATH = -L../../lib
LIBS = -lfeilian_common -lfeilian_parallel

# 源文件
SOURCES = OversetPerformanceMonitor.cpp
HEADERS = OversetPerformanceMonitor.h

# 测试文件
TEST_SOURCES = test_OversetPerformanceMonitor.cpp
EXAMPLE_SOURCES = OversetPerformanceMonitor_Usage_Example.cpp

# 目标文件
OBJECTS = $(SOURCES:.cpp=.o)
TEST_OBJECTS = $(TEST_SOURCES:.cpp=.o)
EXAMPLE_OBJECTS = $(EXAMPLE_SOURCES:.cpp=.o)

# 可执行文件
TEST_EXECUTABLE = test_OversetPerformanceMonitor
EXAMPLE_EXECUTABLE = OversetPerformanceMonitor_Usage_Example

# 默认目标
all: $(OBJECTS) $(TEST_EXECUTABLE) $(EXAMPLE_EXECUTABLE)

# 编译规则
%.o: %.cpp $(HEADERS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 测试程序
$(TEST_EXECUTABLE): $(OBJECTS) $(TEST_OBJECTS)
	$(CXX) $(CXXFLAGS) $(OBJECTS) $(TEST_OBJECTS) $(LIBPATH) $(LIBS) -o $@

# 示例程序
$(EXAMPLE_EXECUTABLE): $(OBJECTS) $(EXAMPLE_OBJECTS)
	$(CXX) $(CXXFLAGS) $(OBJECTS) $(EXAMPLE_OBJECTS) $(LIBPATH) $(LIBS) -o $@

# 仅编译库文件
lib: $(OBJECTS)

# 运行测试
test: $(TEST_EXECUTABLE)
	mpirun -np 2 ./$(TEST_EXECUTABLE)

# 运行示例
example: $(EXAMPLE_EXECUTABLE)
	mpirun -np 4 ./$(EXAMPLE_EXECUTABLE)

# 清理
clean:
	rm -f $(OBJECTS) $(TEST_OBJECTS) $(EXAMPLE_OBJECTS)
	rm -f $(TEST_EXECUTABLE) $(EXAMPLE_EXECUTABLE)
	rm -f *.info *.txt *.csv *.json

# 安装头文件到公共目录
install: $(HEADERS)
	cp $(HEADERS) ../../include/

# 代码格式化
format:
	clang-format -i *.cpp *.h

# 静态分析
analyze:
	cppcheck --enable=all --std=c++11 $(SOURCES) $(HEADERS)

# 性能分析
profile: $(TEST_EXECUTABLE)
	mpirun -np 2 valgrind --tool=callgrind ./$(TEST_EXECUTABLE)

# 内存检查
memcheck: $(TEST_EXECUTABLE)
	mpirun -np 2 valgrind --tool=memcheck --leak-check=full ./$(TEST_EXECUTABLE)

# 帮助信息
help:
	@echo "可用的make目标:"
	@echo "  all      - 编译所有文件"
	@echo "  lib      - 仅编译库文件"
	@echo "  test     - 编译并运行测试程序"
	@echo "  example  - 编译并运行示例程序"
	@echo "  clean    - 清理编译文件"
	@echo "  install  - 安装头文件"
	@echo "  format   - 格式化代码"
	@echo "  analyze  - 静态代码分析"
	@echo "  profile  - 性能分析"
	@echo "  memcheck - 内存泄漏检查"
	@echo "  help     - 显示此帮助信息"

.PHONY: all lib test example clean install format analyze profile memcheck help
