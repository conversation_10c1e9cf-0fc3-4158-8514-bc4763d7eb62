#include "feilian-specialmodule/oversetMesh/DonorSearcher.h"

namespace Floga
{
    DonorSearcher::DonorSearcher(Mesh *mesh_,
                                 ZoneManager *zoneManager_,
                                 ElementField<int> *elemTypeField_)
        : localMesh(mesh_), zoneManager(zoneManager_), elemTypeField(elemTypeField_),
          processorID(GetMPIRank()), nProcessor(GetMPISize())
    {
        n_Zones = zoneManager->GetZoneNum();
        dim = localMesh->GetMeshDimension();

        // 创建各功能模块
        performanceMonitor = std::make_unique<PerformanceMonitor>();
        parallelCoordinator = std::make_unique<ParallelCoordinator>(
            mpi_world, zoneManager, localMesh, performanceMonitor.get());
        dynamicMeshManager = std::make_unique<DynamicMeshManager>();
        toleranceManager = std::make_unique<ToleranceManager>();
        kdtManager = std::make_unique<KDTManager>(dim, zoneManager);
    }

    DonorSearcher::~DonorSearcher()
    {
        Clear();
    }

    void DonorSearcher::Initialize()
    {
        // 清理之前的数据
        Clear();

        // 初始化KDT管理器
        if (!kdtManager->isInitialized())
        {
            kdtManager->initialize(dim, zoneManager);
        }
        kdtManager->initializeKDTSearchers(localMesh, n_Zones);

        // 初始化容差管理器
        toleranceManager->initialize(localMesh);

        // 重置性能指标
        performanceMonitor->resetAllMetrics();

        // 初始化贡献单元标记
        acceptorDonorFlag.assign(localMesh->GetElementNumberReal(), 0);

        // 初始化缓存设置
        enableCache = true;
        maxCacheSize = 100000;
        cacheHitCount = 0;
        cacheMissCount = 0;
    }

    // ==================== 搜索结果缓存管理 ====================

    const Acceptor *DonorSearcher::GetSearchResult(int elemID) const
    {
        // 返回Acceptor
        auto it = searchResults.find(elemID);
        if (it != searchResults.end())
        {
            return &it->second;
        }
        else
        {
            return nullptr;
        }
    }

    void DonorSearcher::ClearSearchResults()
    {
        searchResults.clear();
    }

    size_t DonorSearcher::GetSearchResultsSize() const
    {
        return searchResults.size();
    }

    DonorSearcher::SearchResultsCacheStats DonorSearcher::GetSearchResultsCacheStats() const
    {
        SearchResultsCacheStats stats;
        stats.totalCached = searchResults.size();
        stats.validResults = 0;
        stats.invalidResults = 0;

        for (const auto &acpt : searchResults)
        {
            if (acpt.second.GetCentralDonorID() >= 0)
            {
                stats.validResults++;
            }
            else
            {
                stats.invalidResults++;
            }
        }

        stats.memoryUsage = searchResults.size() * (sizeof(int) + sizeof(Acceptor));

        return stats;
    }

    void DonorSearcher::InsertSearchResult(const Acceptor &actp)
    {
        // 检查缓存中是否已经存在该Acceptor
        auto it = searchResults.find(actp.GetAcceptorID());
        if (it != searchResults.end())
        {
            it->second.SetCentralDonor(actp.GetCentralDonorID(),
                                       actp.GetCentralDonorProcID(),
                                       actp.GetCentralDonorVolume(),
                                       actp.GetCentralDonorType());
        }
        else
        {
            // 如果不存在，直接插入
            searchResults.insert({actp.GetAcceptorID(), actp});
        }
    }

    void DonorSearcher::InsertSearchResults(const Set<Acceptor> &acceptors)
    {
        for (const auto &acpt : acceptors)
        {
            InsertSearchResult(acpt);
        }
    }

    // ==================== 核心搜索功能 ====================

    void DonorSearcher::ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts,
                                            Set<Acceptor> &searchResults)
    {
        // 使用并行协调器执行搜索
        auto searchFunction = [this](List<Acceptor> &acptList)
        {
            this->ChunkDonorSearch(acptList);
        };

        parallelCoordinator->executeParallelSearch(groupedAcpts, searchResults, searchFunction);
    }

    void DonorSearcher::ChunkDonorSearch(List<Acceptor> &acptList)
    {
        if (acptList.size() == 0)
        {
            return;
        }

        for (auto &acpt : acptList)
        {
            bool foundResult = false;
            const int elemID = acpt.GetAcceptorID();

            // 首先检查搜索结果缓存
            if (this->enableCache)
            {
                const Acceptor *cachedResult = this->GetSearchResult(elemID);
                if (cachedResult != nullptr && cachedResult->GetCentralDonorID() >= 0)
                {
                    acpt = *cachedResult;
                    foundResult = true;
                    this->cacheHitCount++;
                }
                else
                {
                    this->cacheMissCount++;
                }
            }

            // 如果缓存中没有找到，且启用了动态网格，尝试从动态网格缓存获取结果
            if (!foundResult && this->dynamicMeshManager->isDynamicMeshEnabled())
            {
                Acceptor dynamicCachedResult;
                if (this->dynamicMeshManager->tryGetCachedResult(acpt.GetAcceptorID(),
                                                                 acpt.GetAcceptorCenter(),
                                                                 dynamicCachedResult))
                {
                    acpt = dynamicCachedResult;
                    foundResult = true;
                }
                else if (this->dynamicMeshManager->tryInheritFromPreviousStep(acpt.GetAcceptorID(),
                                                                              acpt.GetAcceptorCenter(),
                                                                              dynamicCachedResult))
                {
                    acpt = dynamicCachedResult;
                    foundResult = true;
                }
            }

            // 如果都没有找到缓存结果，执行实际搜索
            if (!foundResult)
            {
                this->performActualSearch(acpt);

                // 如果启用了动态网格，缓存搜索结果到动态网格管理器
                if (this->dynamicMeshManager->isDynamicMeshEnabled() && acpt.GetCentralDonorID() >= 0)
                {
                    this->dynamicMeshManager->cacheSearchResult(acpt, acpt.GetAcceptorCenter());
                }
            }

            // 更新搜索结果缓存
            if (this->enableCache)
            {
                this->InsertSearchResult(acpt);
            }
        }
    }

    int DonorSearcher::DonorSearchWithKDT(const Node &srcNode, int zoneID)
    {
        auto startTime = this->performanceMonitor->recordSearchStart();

        KDT *searcher = this->kdtManager->getKDTSearcher(zoneID);
        if (searcher == nullptr)
        {
            this->performanceMonitor->recordSearchEnd(startTime, false, zoneID);
            return -1;
        }

        int donorID = -1;
        searcher->SearchDonorForTgtnode(srcNode, donorID, this->localMesh);

        bool successful = (donorID >= 0);
        this->performanceMonitor->recordSearchEnd(startTime, successful, zoneID);

        if (successful)
        {
            const int &elemType = this->elemTypeField->GetValue(donorID);
            if (elemType == static_cast<int>(ElemOverType::ACCEPTOR))
            {
                this->acceptorDonorFlag[donorID] = 1;
            }
        }

        return donorID;
    }

    void DonorSearcher::GroupingAcceptors(Set<int> &searchElemID,
                                          List<List<Acceptor>> &groupedAcceptors)
    {
        const std::vector<TreeInfo> &globalTreeInfo = this->kdtManager->getGlobalTreeInfo();
        this->parallelCoordinator->groupAcceptors(searchElemID, groupedAcceptors,
                                                  OversetParallelCoordinator::GroupingStrategy::SMART,
                                                  globalTreeInfo);
    }

    void DonorSearcher::GroupingAcceptors(const Set<Acceptor> &srcAcpts,
                                          List<List<Acceptor>> &groupedAcpts)
    {
        this->parallelCoordinator->groupAcceptorsByDonor(srcAcpts, groupedAcpts);
    }

    bool DonorSearcher::NodeInElem(const Node &node, const Element &elem)
    {
        // 使用容差管理器获取适应性容差
        Scalar tolerance = this->toleranceManager->getToleranceForElement(elem);

        // 使用面法向量判断法，改进的容差处理
        const Node &elemCenter = elem.GetCenter();
        const int faceSize = elem.GetFaceSize();

        int insideCount = 0;

        for (int faceI = 0; faceI < faceSize; faceI++)
        {
            const Face &face = this->localMesh->GetFace(elem.GetFaceID(faceI));
            const Node &faceCenter = face.GetCenter();

            // 获取归一化的面法向量
            Vector normal = face.GetNormal();
            normal.Normalize();

            // 使用点到面的距离判断
            Scalar dist1 = normal & (elemCenter - faceCenter);
            Scalar dist2 = normal & (node - faceCenter);

            // 特殊情况：点在面上
            if (std::abs(dist2) < tolerance)
            {
                insideCount++;
                continue;
            }

            // 一般情况：判断点是否在面的同一侧
            if (dist1 * dist2 > -tolerance)
            {
                insideCount++;
            }
        }

        return insideCount == faceSize;
    }

    const std::vector<TreeInfo> &DonorSearcher::GetGlobalTreeInfo() const
    {
        return this->kdtManager->getGlobalTreeInfo();
    }

    void DonorSearcher::Clear()
    {
        acceptorDonorFlag.clear();
        searchResults.clear();
        cacheHitCount = 0;
        cacheMissCount = 0;
    }

    // ==================== 便捷接口实现 ====================

    void DonorSearcher::enableDynamicMesh(Scalar rebuildThreshold, double cacheValidityTime)
    {
        dynamicMeshManager = std::make_unique<DynamicMeshManager>(rebuildThreshold, cacheValidityTime);
        dynamicMeshManager->enableDynamicMesh(localMesh->GetElementNumberReal());
    }

    void DonorSearcher::updateMeshMotion(const std::vector<Vector> &displacementField,
                                         double timeStep, int stepNumber)
    {
        if (dynamicMeshManager->isDynamicMeshEnabled())
        {
            // 网格运动时清理搜索结果缓存，因为位置变化可能导致缓存失效
            if (enableCache)
            {
                clearSearchResults();

                if (performanceMonitor)
                {
                    performanceMonitor->recordEvent("SearchResultsCache", "ClearedForMeshMotion",
                                                    static_cast<double>(stepNumber));
                }
            }

            dynamicMeshManager->updateMeshMotion(displacementField, timeStep, stepNumber);
        }
    }

    const OversetPerformanceMonitor::SearchMetrics &DonorSearcher::getSearchMetrics() const
    {
        return performanceMonitor->getSearchMetrics();
    }

    const OversetDynamicMeshManager::DynamicMeshMetrics &DonorSearcher::getDynamicMeshMetrics() const
    {
        return dynamicMeshManager->getMetrics();
    }

    void DonorSearcher::setGroupingStrategy(OversetParallelCoordinator::GroupingStrategy strategy)
    {
        parallelCoordinator->setGroupingStrategy(strategy);
    }

    void DonorSearcher::setToleranceStrategy(OversetToleranceManager::ToleranceStrategy strategy)
    {
        toleranceManager->setToleranceStrategy(strategy);
    }

    // ==================== 私有方法实现 ====================

    void DonorSearcher::performActualSearch(Acceptor &acpt)
    {
        const Node &elemCenter = acpt.GetAcceptorCenter();
        const int &donorID = acpt.GetCentralDonorID();
        const int &donorProcID = acpt.GetCentralDonorProcID();

        // 首先检查是否已有有效的贡献单元
        if (donorID >= 0 && donorProcID == processorID)
        {
            const Element &elem = localMesh->GetElement(donorID);
            if (NodeInElem(elemCenter, elem))
            {
                const int &newDonorType = elemTypeField->GetValue(donorID);
                const Scalar &newDonorVolume = elem.GetVolume();
                acpt.SetCentralDonor(donorID, donorProcID, newDonorVolume, newDonorType);
                return;
            }
        }

        // 在各个子域的KDT搜索器中搜索贡献单元
        for (int zoneI = 0; zoneI < n_Zones; zoneI++)
        {
            if (acpt.GetAcceptorZoneID() != zoneI) // 仅搜索其他子域
            {
                int newDonorID = DonorSearchWithKDT(elemCenter, zoneI);
                if (newDonorID >= 0)
                {
                    Scalar elemVolume = localMesh->GetElement(newDonorID).GetVolume();
                    acpt.SetCentralDonor(newDonorID,
                                         processorID,
                                         elemVolume,
                                         elemTypeField->GetValue(newDonorID));
                    break;
                }
            }
        }
    }

    // ==================== 私有方法实现 ====================

    const Acceptor *DonorSearcher::getSearchResult(int elemID) const
    {
        return GetSearchResult(elemID);
    }

    void DonorSearcher::updateSearchResult(int elemID, const Acceptor &acceptor)
    {
        InsertSearchResult(acceptor);
    }

} // namespace Floga
